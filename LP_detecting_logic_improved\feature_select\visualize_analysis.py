#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电池分析数据可视化工具
对分析CSV文件中的数值列进行归一化并可视化

功能：
1. 读取分析CSV文件
2. 对数值列进行归一化（-1到1）
3. 创建多子图可视化
4. 支持批量处理

使用方法：
python visualize_analysis.py input_file.csv
python visualize_analysis.py folder_path/  # 批量处理

作者：Claude AI
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import os
from pathlib import Path
import warnings

warnings.filterwarnings("ignore")

# 设置字体为Times New Roman
plt.rcParams["font.family"] = "serif"
plt.rcParams["font.serif"] = ["Times New Roman", "Times", "DejaVu Serif"]
plt.rcParams["axes.unicode_minus"] = False


class BatteryDataVisualizer:
    def __init__(self):
        """初始化可视化器"""
        self.numeric_columns = []
        self.categorical_columns = []

    def normalize_data(self, data):
        """
        归一化数据到[-1, 1]范围
        """
        return 2 * (data - data.min()) / (data.max() - data.min()) - 1

    def get_column_groups(self, df):
        """
        根据图片布局分组列
        """
        # 基于图片布局的列分组
        voltage_metrics = [
            "cv_avg_current_A",
            "cv_min_current_A",
            "cv_max_current_A",
            "cv_duration_ratio",
        ]
        cc_metrics = [
            "coulombic_efficiency",
            "coulombic_efficiency_deviation",
            "cv_segment_duration_s",
            "capacity_retention_percent",
        ]
        current_metrics = [
            "discharge_capacity_Ah",
            "capacity_fade_rate_Ah_per_cycle",
            "capacity_fade_rate_percent_per_cycle",
            "test_time_hours",
        ]
        cv_metrics = ["S_plat", "S_bump", "S_mono", "S_amp"]

        # 检查哪些列存在于数据中
        available_voltage = [col for col in voltage_metrics if col in df.columns]
        available_cc = [col for col in cc_metrics if col in df.columns]
        available_current = [col for col in current_metrics if col in df.columns]
        available_cv = [col for col in cv_metrics if col in df.columns]

        return {
            "voltage": available_voltage,
            "cc": available_cc,
            "current": available_current,
            "cv": available_cv,
        }

    def create_visualization(self, df, output_path, cycle_range=None):
        """
        创建可视化图表

        Args:
            df: 数据DataFrame
            output_path: 输出路径
            cycle_range: 循环范围元组 (start, end)，如果为None则使用全部数据
        """
        # 过滤循环范围
        if cycle_range is not None:
            start_cycle, end_cycle = cycle_range
            if end_cycle is None:
                # 只指定起始循环，到最后
                df_filtered = df[df["cycle_num"] >= start_cycle].copy()
                actual_end = df["cycle_num"].max()
                print(f"   使用循环范围: {start_cycle} - {actual_end} (最后)")
            else:
                # 指定了起始和结束循环
                df_filtered = df[
                    (df["cycle_num"] >= start_cycle) & (df["cycle_num"] <= end_cycle)
                ].copy()
                print(f"   使用循环范围: {start_cycle} - {end_cycle}")
            print(f"   过滤后数据点: {len(df_filtered)}")
        else:
            df_filtered = df.copy()

        # 获取数值列
        numeric_cols = df_filtered.select_dtypes(include=[np.number]).columns.tolist()
        if "cycle_num" in numeric_cols:
            numeric_cols.remove("cycle_num")

        # 获取列分组
        column_groups = self.get_column_groups(df_filtered)

        # 创建4x4子图布局
        fig, axes = plt.subplots(4, 4, figsize=(16, 12))
        fig.suptitle(
            "Battery Analysis Data Visualization (Normalized)",
            fontsize=18,
            fontweight="bold",
        )

        # 使用viridis颜色映射为循环数创建渐变色
        cycle_numbers = df_filtered["cycle_num"].values
        norm = plt.Normalize(vmin=cycle_numbers.min(), vmax=cycle_numbers.max())
        colormap = plt.cm.viridis

        plot_idx = 0

        # 绘制每组数据
        groups = ["voltage", "cc", "current", "cv"]
        group_titles = [
            "Voltage Metrics",
            "CC Metrics",
            "Current Metrics",
            "CV Metrics",
        ]

        for group_idx, (group_name, group_title) in enumerate(
            zip(groups, group_titles)
        ):
            cols = column_groups[group_name]

            for col_idx, col in enumerate(cols):
                if plot_idx >= 16:  # 最多16个子图
                    break

                row = plot_idx // 4
                col_pos = plot_idx % 4
                ax = axes[row, col_pos]

                if col in df_filtered.columns:
                    # 归一化数据
                    normalized_data = self.normalize_data(df_filtered[col])

                    # 绘制散点图，使用viridis渐变色
                    scatter = ax.scatter(
                        df_filtered["cycle_num"],
                        normalized_data,
                        c=cycle_numbers,
                        cmap=colormap,
                        norm=norm,
                        alpha=0.8,
                        s=6,
                        edgecolors="none",
                    )

                    # 设置标题和标签
                    ax.set_title(f"{col}", fontsize=14, fontweight="bold", pad=10)
                    ax.set_xlabel("Cycle Number", fontsize=12)
                    ax.set_ylabel("Normalized Value", fontsize=12)
                    ax.grid(True, alpha=0.2, linewidth=0.5)
                    ax.set_ylim(-1.05, 1.05)

                    # 设置刻度样式
                    ax.tick_params(labelsize=12, width=1.0, length=4)
                    # 保留四边边框
                    ax.spines["top"].set_linewidth(1.5)
                    ax.spines["right"].set_linewidth(1.5)
                    ax.spines["left"].set_linewidth(1.5)
                    ax.spines["bottom"].set_linewidth(1.5)

                    # 在右上角添加编号
                    ax.text(
                        0.95,
                        0.95,
                        f"{plot_idx+1}",
                        transform=ax.transAxes,
                        bbox=dict(
                            boxstyle="round,pad=0.2",
                            facecolor="white",
                            alpha=0.9,
                            edgecolor="gray",
                            linewidth=0.5,
                        ),
                        fontsize=12,
                        ha="right",
                        va="top",
                    )

                plot_idx += 1

        # 如果还有剩余的数值列，继续绘制
        remaining_cols = [
            col for col in numeric_cols if col not in sum(column_groups.values(), [])
        ]
        for col in remaining_cols:
            if plot_idx >= 16:
                break

            row = plot_idx // 4
            col_pos = plot_idx % 4
            ax = axes[row, col_pos]

            # 归一化数据
            normalized_data = self.normalize_data(df_filtered[col])

            # 绘制散点图，使用viridis渐变色
            scatter = ax.scatter(
                df_filtered["cycle_num"],
                normalized_data,
                c=cycle_numbers,
                cmap=colormap,
                norm=norm,
                alpha=0.8,
                s=6,
                edgecolors="none",
            )

            # 设置标题和标签
            ax.set_title(f"{col}", fontsize=14, fontweight="bold", pad=10)
            ax.set_xlabel("Cycle Number", fontsize=12)
            ax.set_ylabel("Normalized Value", fontsize=12)
            ax.grid(True, alpha=0.2, linewidth=0.5)
            ax.set_ylim(-1.05, 1.05)

            # 设置刻度样式
            ax.tick_params(labelsize=12, width=1.0, length=4)
            # 保留四边边框
            ax.spines["top"].set_linewidth(1.5)
            ax.spines["right"].set_linewidth(1.5)
            ax.spines["left"].set_linewidth(1.5)
            ax.spines["bottom"].set_linewidth(1.5)

            # 在右上角添加编号
            ax.text(
                0.95,
                0.95,
                f"{plot_idx+1}",
                transform=ax.transAxes,
                bbox=dict(
                    boxstyle="round,pad=0.2",
                    facecolor="white",
                    alpha=0.9,
                    edgecolor="gray",
                    linewidth=0.5,
                ),
                fontsize=12,
                ha="right",
                va="top",
            )

            plot_idx += 1

        # 隐藏未使用的子图
        for i in range(plot_idx, 16):
            row = i // 4
            col_pos = i % 4
            axes[row, col_pos].set_visible(False)

        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.96])

        # 保存图片
        plt.savefig(output_path, dpi=300, bbox_inches="tight", facecolor="white")
        plt.close()

        print(f"✅ 可视化图片已保存: {output_path}")

    def save_normalized_data(self, df, output_path, cycle_range=None):
        """
        保存归一化后的数据

        Args:
            df: 数据DataFrame
            output_path: 输出路径
            cycle_range: 循环范围元组 (start, end)，如果为None则使用全部数据
        """
        # 过滤循环范围
        if cycle_range is not None:
            start_cycle, end_cycle = cycle_range
            if end_cycle is None:
                # 只指定起始循环，到最后
                df_filtered = df[df["cycle_num"] >= start_cycle].copy()
            else:
                # 指定了起始和结束循环
                df_filtered = df[
                    (df["cycle_num"] >= start_cycle) & (df["cycle_num"] <= end_cycle)
                ].copy()
        else:
            df_filtered = df.copy()

        df_normalized = df_filtered.copy()

        # 获取数值列（除了cycle_num）
        numeric_cols = df_filtered.select_dtypes(include=[np.number]).columns.tolist()
        if "cycle_num" in numeric_cols:
            numeric_cols.remove("cycle_num")

        # 对每个数值列进行归一化
        for col in numeric_cols:
            df_normalized[f"{col}_normalized"] = self.normalize_data(df_filtered[col])

        # 保存归一化数据
        df_normalized.to_csv(output_path, index=False)
        print(f"✅ 归一化数据已保存: {output_path}")

    def process_file(self, file_path, cycle_range=None):
        """
        处理单个CSV文件

        Args:
            file_path: CSV文件路径
            cycle_range: 循环范围元组 (start, end)，如果为None则使用全部数据
        """
        try:
            print(f"\n📊 处理文件: {file_path}")

            # 读取CSV文件
            df = pd.read_csv(file_path)
            print(f"   数据形状: {df.shape}")
            print(f"   循环数范围: {df['cycle_num'].min()} - {df['cycle_num'].max()}")

            # 检查是否有cycle_num列
            if "cycle_num" not in df.columns:
                print(f"❌ 错误: 文件 {file_path} 中没有 'cycle_num' 列")
                return False

            # 获取输出路径
            file_path = Path(file_path)
            output_dir = file_path.parent
            base_name = file_path.stem

            # 创建可视化
            viz_path = output_dir / f"{base_name}_visualization_allcycles.png"
            self.create_visualization(df, viz_path, cycle_range)

            # 保存归一化数据
            normalized_path = output_dir / f"{base_name}_normalized_allcycles.csv"
            self.save_normalized_data(df, normalized_path, cycle_range)

            return True

        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
            return False

    def process_directory(self, dir_path, cycle_range=None):
        """
        批量处理目录中的CSV文件

        Args:
            dir_path: 目录路径
            cycle_range: 循环范围元组 (start, end)，如果为None则使用全部数据
        """
        dir_path = Path(dir_path)
        csv_files = list(dir_path.glob("*_analysis.csv"))

        if not csv_files:
            print(f"❌ 在目录 {dir_path} 中没有找到分析CSV文件（*_analysis.csv）")
            return

        print(f"📁 找到 {len(csv_files)} 个分析CSV文件")

        success_count = 0
        for csv_file in csv_files:
            if self.process_file(csv_file, cycle_range):
                success_count += 1

        print(f"\n✅ 批量处理完成: {success_count}/{len(csv_files)} 个文件处理成功")


def main():
    parser = argparse.ArgumentParser(description="电池分析数据可视化工具")
    parser.add_argument("input", help="输入CSV文件路径或目录路径")
    parser.add_argument("--dpi", type=int, default=300, help="图片分辨率 (默认: 300)")
    parser.add_argument(
        "--cycle-range",
        type=str,
        help="循环范围，支持两种格式: 1) start,end (例如: 10,500) 2) start (例如: 20，表示从第20圈到最后)",
    )

    args = parser.parse_args()

    # 解析循环范围
    cycle_range = None
    if args.cycle_range:
        try:
            parts = args.cycle_range.split(",")
            if len(parts) == 2:
                # 格式: start,end
                start, end = map(int, parts)
                cycle_range = (start, end)
                print(f"🎯 指定循环范围: {start} - {end}")
            elif len(parts) == 1:
                # 格式: start（表示从start到最后）
                start = int(parts[0])
                cycle_range = (start, None)  # None表示到最后
                print(f"🎯 指定循环范围: {start} - 最后")
            else:
                print("❌ 错误: 循环范围格式错误")
                print("   支持格式: 1) start,end (例如: 10,500) 2) start (例如: 20)")
                return
        except ValueError:
            print("❌ 错误: 循环范围必须为整数")
            print("   支持格式: 1) start,end (例如: 10,500) 2) start (例如: 20)")
            return

    # 创建可视化器
    visualizer = BatteryDataVisualizer()

    input_path = Path(args.input)

    if input_path.is_file():
        # 单文件处理
        if input_path.suffix.lower() == ".csv":
            visualizer.process_file(input_path, cycle_range)
        else:
            print("❌ 错误: 请提供CSV文件")
    elif input_path.is_dir():
        # 批量处理
        visualizer.process_directory(input_path, cycle_range)
    else:
        print(f"❌ 错误: 路径 {input_path} 不存在")


if __name__ == "__main__":
    main()
