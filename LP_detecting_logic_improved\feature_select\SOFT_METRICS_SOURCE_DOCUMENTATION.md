# 软指标计算技术实现文档
# Technical Implementation Documentation for Soft Metrics Calculation

## 📋 文档概述

本文档专注于电池析锂检测系统中七个核心软指标的**技术实现细节**、算法原理和数学公式。

> 📚 **用户使用指南**请参考：`soft_metrics_analyzer_guide.md`
> 🔧 **本文档面向**：开发者、算法工程师、技术维护人员

## 🎯 软指标计算架构

### 核心模块关系图
```
软指标分析器生态系统（生产版本 - 已清理测试代码）
├── soft_metrics_analyzer.py          # 🆕 自包含主程序（生产版本）
│   ├── 命令行解析和参数配置
│   ├── JSON数据加载和预处理
│   ├── 两阶段采样算法（内置）
│   ├── 七个软指标计算函数（内置）
│   ├── 数据质量验证（内置）
│   ├── 文件批量处理逻辑
│   └── 结果整合和输出格式化
│
├── lithium_plating_diagnostic.py     # 智能诊断模块（可选）
│   ├── 基线建立和偏离度计算
│   ├── 趋势分析和特征工程
│   ├── 分层诊断和风险评估
│   └── 预测和建议生成
│
└── visualize_analysis.py            # 可视化工具
    ├── 数据归一化处理
    ├── 多指标趋势可视化
    └── 学术论文级图表生成
```

### 🆕 版本更新说明
- **v2.0 生产版本**: 已清理所有测试代码，专注核心功能
- **代码优化**: 减少373行测试代码，提高可读性和维护性
- **功能完整**: 保留所有核心分析功能，无功能损失
- **性能提升**: 文件更小，加载更快，专注生产环境使用

## 🔬 七个软指标详细来源

### 1. 平台度 (S_plat) 
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_platform_degree()`

**🔬 物理原理**: 
- 析锂发生时，电流-时间曲线出现平台现象
- 平台特征反映了锂离子在负极表面的沉积过程

**📊 数学定义**:
```python
def compute_s_plat(self, segment_data):
    """
    计算电流平台度
    
    S_plat = t_platform / t_total
    
    其中:
    - t_platform: |dI/dt| < tau_slope 的总时间
    - t_total: 分析段总时间  
    - tau_slope: 斜率阈值参数 (默认 0.002 A/s)
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 计算电流斜率
    dI_dt = np.gradient(I, t)
    
    # 识别平台段 (斜率接近零)
    platform_mask = np.abs(dI_dt) < self.config['physics_params']['tau_slope']
    
    # 计算平台时间占比
    t_platform = np.sum(platform_mask) * np.mean(np.diff(t))
    t_total = t[-1] - t[0]
    
    return t_platform / t_total if t_total > 0 else 0.0
```

**⚙️ 关键参数**:
- `tau_slope = 0.002`: 斜率阈值，控制平台检测敏感度
- 建议调整范围: 0.001 - 0.005

### 2. 凸起度 (S_bump)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_bump_degree()`

**🔬 物理原理**:
- 析锂过程中电流出现先升后降的凸起模式
- 反映锂离子沉积的非均匀性和表面形貌变化

**📊 数学定义**:
```python
def compute_s_bump(self, segment_data):
    """
    计算电流凸起度
    
    S_bump = N_bump_sequences / N_total_windows
    
    识别先正后负的曲率序列模式
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 计算二阶导数 (曲率)
    d2I_dt2 = np.gradient(np.gradient(I, t), t)
    
    # 识别正负曲率序列
    pos_curvature = d2I_dt2 > self.config['physics_params']['kappa_pos']
    neg_curvature = d2I_dt2 < self.config['physics_params']['kappa_neg']
    
    # 检测凸起模式 (正曲率后跟负曲率)
    bump_count = self._detect_bump_sequences(pos_curvature, neg_curvature)
    
    return bump_count / len(d2I_dt2) if len(d2I_dt2) > 0 else 0.0
```

**⚙️ 关键参数**:
- `kappa_pos = 0.0005`: 正曲率阈值
- `kappa_neg = -0.0005`: 负曲率阈值

### 3. 非单调性 (S_mono)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_non_monotonicity()`

**🔬 物理原理**:
- 正常充电电流应单调递减
- 析锂破坏电流的单调性，产生局部上升

**📊 数学定义**:
```python
def compute_s_mono(self, segment_data):
    """
    计算非单调性指标
    
    S_mono = Σ|I(t) - I_baseline(t)| / Σ|I_baseline(t)|
    
    其中 I_baseline(t) = I(0) * α^(t/τ)
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 构建指数衰减基线
    alpha = self.config['physics_params']['alpha_baseline']
    tau = (t[-1] - t[0]) / 10  # 时间常数
    I_baseline = I[0] * (alpha ** ((t - t[0]) / tau))
    
    # 计算偏离程度
    deviation = np.sum(np.abs(I - I_baseline))
    baseline_sum = np.sum(np.abs(I_baseline))
    
    return deviation / baseline_sum if baseline_sum > 0 else 0.0
```

**⚙️ 关键参数**:
- `alpha_baseline = 0.98`: 基线衰减因子

### 4. 回嵌幅度 (S_amp)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_reintercalation_amplitude()`

**🔬 物理原理**:
- 析锂导致电流高于预期基线
- 反映锂离子回嵌过程的强度

**📊 数学定义**:
```python
def compute_s_amp(self, segment_data):
    """
    计算回嵌幅度
    
    S_amp = max(0, max(I(t) - I_baseline(t))) / I_max
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 构建单调基线
    I_baseline = self._construct_monotonic_baseline(t, I)
    
    # 计算最大正偏差
    positive_deviations = np.maximum(0, I - I_baseline)
    max_amplitude = np.max(positive_deviations)
    I_max = np.max(I)
    
    return max_amplitude / I_max if I_max > 0 else 0.0
```

### 5. 锂量代理 (Q_tilde)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_lithium_proxy()`

**🔬 物理原理**:
- 析锂过程中的额外电荷累积
- 通过电流异常部分的时间积分估算

**📊 数学定义**:
```python
def compute_q_tilde(self, segment_data):
    """
    计算锂量代理指标
    
    Q_tilde = ∫(I_anomaly(t) dt)
    其中 I_anomaly(t) = max(0, I(t) - I_expected(t))
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 构建预期电流曲线
    I_expected = self._construct_expected_current(t, I)
    
    # 计算异常电流
    I_anomaly = np.maximum(0, I - I_expected)
    
    # 时间积分
    Q_tilde = np.trapz(I_anomaly, t) / 3600  # 转换为Ah
    
    return Q_tilde
```

### 6. 低电流一致性 (C_low)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_low_current_consistency()`

**🔬 物理原理**:
- 析锂影响低电流区域的稳定性
- 通过变异系数评估一致性

**📊 数学定义**:
```python
def compute_c_low(self, segment_data):
    """
    计算低电流一致性
    
    C_low = 1 - (σ_low / μ_low)
    其中分析 I < i_threshold 的区域
    """
    I = segment_data['I']
    i_threshold = self.config['physics_params']['i_threshold']
    
    # 提取低电流区域
    low_current_mask = I < i_threshold
    I_low = I[low_current_mask]
    
    if len(I_low) < 2:
        return 1.0  # 无足够低电流数据，假设一致
    
    # 计算变异系数
    mean_low = np.mean(I_low)
    std_low = np.std(I_low)
    
    cv = std_low / mean_low if mean_low > 0 else 0
    
    return max(0, 1 - cv)  # 一致性指标
```

**⚙️ 关键参数**:
- `i_threshold = 0.1`: 低电流阈值 (A)

### 7. 曲率一致性 (C_kappa)
**📍 源码位置**: `soft_metrics_analyzer.py` → `LithiumDetectionDataLoader._compute_curvature_consistency()`

**🔬 物理原理**:
- 析锂破坏电流曲线的平滑性
- 通过二阶导数评估曲率变化

**📊 数学定义**:
```python
def compute_c_kappa(self, segment_data):
    """
    计算曲率一致性
    
    C_kappa = 1 - (σ_kappa / (|μ_kappa| + ε))
    其中 κ(t) = d²I/dt²
    """
    t = segment_data['t']
    I = segment_data['I']
    
    # 计算曲率 (二阶导数)
    kappa = np.gradient(np.gradient(I, t), t)
    
    # 计算曲率统计量
    mean_kappa = np.mean(kappa)
    std_kappa = np.std(kappa)
    
    # 一致性指标
    epsilon = 1e-8  # 防除零
    consistency = 1 - (std_kappa / (abs(mean_kappa) + epsilon))
    
    return max(0, consistency)
```

## 📊 数据接口规范

### 输入数据格式
```python
segment_data = {
    't': np.array([0, 1, 2, ...]),      # 时间序列 (s)
    'I': np.array([2.0, 1.8, 1.6, ...]), # 电流序列 (A)
    'V': np.array([4.0, 4.0, 4.0, ...]), # 电压序列 (V) - 可选
    'Q': np.array([0, 0.5, 1.0, ...])    # 容量序列 (Ah) - 可选
}
```

### 输出数据格式
```python
soft_metrics = {
    'S_plat': float,    # 平台度 [0, 1]
    'S_bump': float,    # 凸起度 [0, 1]  
    'S_mono': float,    # 非单调性 [0, 1]
    'S_amp': float,     # 回嵌幅度 [0, 1]
    'Q_tilde': float,   # 锂量代理 [0, +∞]
    'C_low': float,     # 低电流一致性 [0, 1]
    'C_kappa': float    # 曲率一致性 [0, 1]
}
```

> 📋 **完整调用流程** 请参考 `soft_metrics_analyzer_guide.md` 中的详细说明

## 🎯 最佳实践总结

### 参数调优建议
1. **tau_slope**: 根据电池类型和噪声水平调整
2. **kappa_pos/neg**: 基于数据的二阶导数分布调整  
3. **alpha_baseline**: 根据正常充电曲线的衰减特性调整
4. **i_threshold**: 根据CV阶段的典型电流范围调整

### 数据质量要求
1. **采样率**: 建议 ≥ 1Hz，确保捕获电流变化细节
2. **数据完整性**: 避免大段数据缺失
3. **噪声控制**: 信噪比建议 > 20dB
4. **时间同步**: 确保时间戳准确性

### 计算性能优化
1. **向量化**: 使用NumPy向量操作
2. **内存管理**: 及时释放大型数组
3. **并行化**: 多循环可并行计算
4. **缓存**: 重复计算结果可缓存

---

**文档版本**: v2.0 (生产版本)
**最后更新**: 2025-01-10
**维护者**: Claude AI
**版本特性**:
- ✅ 已清理所有测试代码（减少373行）
- ✅ 专注生产环境使用
- ✅ 保留完整核心功能
- ✅ 优化代码结构和性能

**相关文件**:
- `soft_metrics_analyzer_guide.md`: 详细使用指南（已更新）
- `soft_metrics_analyzer.py`: 自包含主程序（生产版本，1890行）
- `lithium_plating_diagnostic.py`: 智能诊断模块（可选）
- `visualization_guide.md`: 可视化工具使用指南
