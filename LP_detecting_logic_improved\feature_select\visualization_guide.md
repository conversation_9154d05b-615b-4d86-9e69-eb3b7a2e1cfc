# 电池数据可视化工具使用指南
# Battery Data Visualization Tool Guide

## 📋 概述

`visualize_analysis.py` 是一个专门用于电池分析数据可视化的工具，支持：
- 数值列归一化处理（-1到1范围）
- 4x4子图布局可视化
- 循环范围过滤功能
- 批量处理功能

## 🚀 快速开始

### 基本用法

```bash
# 单文件处理
python visualize_analysis.py battery_data_analysis.csv

# 批量处理
python visualize_analysis.py analysis_results/

# 指定循环范围（避免前几圈异常影响）
# 🆕 单边界：从第20圈到最后（推荐用法）
python visualize_analysis.py battery_data_analysis.csv --cycle-range 20

# 双边界：指定起始和结束
python visualize_analysis.py battery_data_analysis.csv --cycle-range 10,500

# 自定义图片分辨率
python visualize_analysis.py battery_data_analysis.csv --dpi 600
```

## 📊 参数说明

### 必需参数
- `input`: 输入CSV文件路径或目录路径

### 可选参数
- `--cycle-range`: 循环范围过滤，支持两种格式：
  - **双边界**: `start,end` - 指定起始和结束循环
    - 例如：`--cycle-range 10,500` 表示只使用第10-500圈数据
  - **🆕 单边界**: `start` - 指定起始循环，自动到最后一圈
    - 例如：`--cycle-range 20` 表示从第20圈到最后一圈
  - 用于排除前几圈异常循环的影响
  - 如不指定，则使用全部数据

- `--dpi`: 图片分辨率，默认300
  - 用于控制输出图片的清晰度
  - 建议值：300（标准）、600（高清）

## 🎯 典型使用场景

### 1. 标准分析（使用全部数据）
```bash
python visualize_analysis.py analysis_results/2C_battery-1_CV_Qcha_CE_analysis.csv
```

### 2. 排除初期异常影响
```bash
# 🆕 推荐用法：排除前20圈数据（自动到最后）
python visualize_analysis.py analysis_results/2C_battery-1_CV_Qcha_CE_analysis.csv --cycle-range 20

# 传统用法：手动指定结束圈数
python visualize_analysis.py analysis_results/2C_battery-1_CV_Qcha_CE_analysis.csv --cycle-range 10,389
```

### 3. 分析特定阶段
```bash
# 仅分析中期阶段（第50-200圈）
python visualize_analysis.py analysis_results/2C_battery-1_CV_Qcha_CE_analysis.csv --cycle-range 50,200

# 仅分析后期阶段（第200圈以后）🆕 
python visualize_analysis.py analysis_results/2C_battery-1_CV_Qcha_CE_analysis.csv --cycle-range 200
```

### 4. 批量处理并排除异常
```bash
# 🆕 批量处理所有文件，统一排除前15圈数据（自动到最后）

```

## 📈 输出文件

### 生成文件类型
1. **可视化图片**: `{filename}_visualization.png`
   - 4x4子图布局，优化的学术风格设计
   - 每个子图显示一个数值指标的归一化趋势
   - 子图按类型分组：电压指标、CC指标、电流指标、CV指标
   - **🆕 viridis渐变色**：点的颜色按循环数渐变显示
   - **优化样式**：更小的字体、更细的网格线、去除顶部和右侧边框

2. **归一化数据**: `{filename}_normalized.csv`
   - 原始数据 + 归一化列
   - 归一化列命名格式：`{原列名}_normalized`
   - 如指定循环范围，则只包含过滤后的数据

### 数据归一化说明
- **归一化范围**: -1 到 1
- **归一化公式**: `2 * (x - min) / (max - min) - 1`
- **适用列**: 所有数值列（除了cycle_num）

## 🔧 高级功能

### 循环范围过滤的优势
1. **排除激活期影响**: 前几圈电池激活过程可能产生异常数据
2. **聚焦稳定期**: 关注电池稳定工作期间的趋势变化
3. **对比分析**: 可以对比不同阶段的数据特征

### 批量处理优势
1. **效率提升**: 一次处理多个文件
2. **统一参数**: 所有文件使用相同的处理参数
3. **自动识别**: 自动识别`*_analysis.csv`格式文件

## 📋 文件要求

### 输入文件格式
- **文件类型**: CSV格式
- **必需列**: `cycle_num`（循环数列）
- **数据类型**: 包含数值列用于可视化
- **文件命名**: 建议使用`*_analysis.csv`格式

### 兼容文件类型
- 软指标分析结果文件
- 电池健康状态数据
- 循环性能数据
- 任何包含cycle_num的时序数据

## ⚠️ 注意事项

1. **循环范围验证**: 确保指定的循环范围在数据范围内
2. **数据完整性**: 确保CSV文件包含完整的数值数据
3. **内存使用**: 大文件处理时注意内存占用
4. **路径格式**: Windows系统建议使用正斜杠或双反斜杠

## 🚨 常见问题

### Q1: 报错"没有找到cycle_num列"
**解决方案**: 确保CSV文件包含名为`cycle_num`的列

### Q2: 图片显示异常或空白
**解决方案**: 
- 检查数据是否包含有效的数值列
- 确认指定的循环范围是否有效
- 检查matplotlib依赖是否正确安装

### Q3: 循环范围格式错误
**解决方案**: 使用正确格式 `start,end`，例如 `10,500`

### Q4: 批量处理找不到文件
**解决方案**: 确保目录中包含`*_analysis.csv`格式的文件

## 📊 示例输出

### 控制台输出示例
```
🎯 指定循环范围: 10 - 300

📊 处理文件: analysis_results\2C_battery-1_CV_Qcha_CE_analysis.csv
   数据形状: (389, 27)
   循环数范围: 1.0 - 389.0
   使用循环范围: 10 - 300
   过滤后数据点: 291
✅ 可视化图片已保存: analysis_results\2C_battery-1_CV_Qcha_CE_analysis_visualization.png
✅ 归一化数据已保存: analysis_results\2C_battery-1_CV_Qcha_CE_analysis_normalized.csv
```

### 图片布局说明
- **4x4布局**: 总共16个子图，学术风格设计
- **编号系统**: 每个子图右上角显示编号（1-16）
- **分组显示**: 按指标类型分组展示
- **标准化轴**: Y轴范围统一为-1.05到1.05
- **🆕 渐变配色**: 使用viridis颜色映射，按循环数渐变
- **🆕 优化样式**: 
  - 字体：Times New Roman（学术标准字体）
  - 图表标题：18pt，粗体
  - 子图标题：14pt，粗体
  - 轴标签字体：12pt
  - 刻度标签：12pt
  - 编号标签：12pt
  - 坐标轴线：1.5pt粗细
  - 网格线：更细更淡（透明度0.2）
  - 边框：保留四边边框，统一1.5pt粗细

## 🔗 相关文件

- `soft_metrics_analyzer.py`: 软指标分析主程序
- `lithium_plating_diagnostic.py`: 析锂诊断模块
- `soft_metrics_analyzer_guide.md`: 软指标分析使用指南
