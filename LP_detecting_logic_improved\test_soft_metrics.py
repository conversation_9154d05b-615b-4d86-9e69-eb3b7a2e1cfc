#!/usr/bin/env python3
"""
软指标提取功能测试脚本
用于验证析锂检测软指标计算的正确性
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from data_loader_XJTU import LithiumDetectionDataLoader, log


def create_test_data():
    """创建测试数据 - 模拟包含析锂特征的CV段"""

    # 时间序列 (10分钟，1秒间隔)
    t = np.linspace(0, 600, 600)

    # 1. 正常的指数衰减基线
    I_normal = 1.8 * np.exp(-t / 400)  # 从1.8A衰减到很小

    # 2. 添加析锂特征
    I_with_lithium = I_normal.copy()

    # 平台特征 (300-400秒)
    platform_region = (t >= 300) & (t <= 400)
    I_with_lithium[platform_region] = np.mean(I_normal[platform_region]) + 0.05

    # 凸起特征 (450-500秒)
    bump_region = (t >= 450) & (t <= 500)
    bump_amplitude = 0.08 * np.sin((t[bump_region] - 450) * 2 * np.pi / 50)
    I_with_lithium[bump_region] += bump_amplitude

    # 添加一些噪声
    noise = np.random.normal(0, 0.005, len(t))
    I_with_lithium += noise

    # 确保电流为正且小于2A
    I_with_lithium = np.maximum(I_with_lithium, 0.001)
    I_with_lithium = np.minimum(I_with_lithium, 1.95)

    # 创建其他数据
    V = np.full_like(t, 4.0) + np.random.normal(0, 0.001, len(t))  # 恒压+小噪声
    Q = np.cumsum(I_with_lithium) * (t[1] - t[0]) / 3600  # 积分得到容量

    return {
        "t": t,
        "I_normal": I_normal,
        "I_with_lithium": I_with_lithium,
        "V": V,
        "Q": Q,
    }


def test_soft_metrics_calculation():
    """测试软指标计算功能"""

    print("=" * 60)
    print("析锂检测软指标计算测试")
    print("=" * 60)

    # 初始化分析器
    config = {
        "physics_params": {
            "tau_slope": 0.002,
            "kappa_pos": 0.0005,
            "kappa_neg": -0.0005,
            "alpha_baseline": 0.98,
            "sigma_p": 0.001,
            "sigma_b": 0.001,
            "i_threshold": 0.1,
        }
    }

    analyzer = LithiumDetectionDataLoader(config)

    # 创建测试数据
    test_data = create_test_data()

    print("1. 测试数据生成完成")
    print(f"   时间范围: {test_data['t'][0]:.1f} - {test_data['t'][-1]:.1f} 秒")
    print(
        f"   电流范围: {np.min(test_data['I_with_lithium']):.3f} - {np.max(test_data['I_with_lithium']):.3f} A"
    )

    # 测试1: 正常电流段
    normal_segment = {
        "t": test_data["t"][:200],  # 前200秒
        "I": test_data["I_normal"][:200],
        "V": test_data["V"][:200],
        "Q": test_data["Q"][:200],
    }

    print("\n2. 计算正常段软指标...")
    normal_metrics = analyzer.compute_seven_soft_metrics(normal_segment)

    # 测试2: 含析锂特征段
    lithium_segment = {
        "t": test_data["t"],
        "I": test_data["I_with_lithium"],
        "V": test_data["V"],
        "Q": test_data["Q"],
    }

    print("3. 计算含析锂特征段软指标...")
    lithium_metrics = analyzer.compute_seven_soft_metrics(lithium_segment)

    # 结果对比
    print("\n" + "=" * 60)
    print("软指标对比结果")
    print("=" * 60)
    print(f"{'指标':>12} {'正常段':>10} {'析锂段':>10} {'差值':>10} {'说明'}")
    print("-" * 60)

    metrics_names = {
        "S_plat": "平台度",
        "S_bump": "凸起度",
        "S_mono": "非单调性",
        "S_amp": "回嵌幅度",
        "Q_tilde": "锂量代理",
        "C_low": "低电流一致性",
        "C_kappa": "曲率一致性",
    }

    for metric in [
        "S_plat",
        "S_bump",
        "S_mono",
        "S_amp",
        "Q_tilde",
        "C_low",
        "C_kappa",
    ]:
        normal_val = normal_metrics[metric]
        lithium_val = lithium_metrics[metric]
        diff = lithium_val - normal_val

        # 判断变化方向
        if metric in ["S_plat", "S_bump", "S_mono", "S_amp"]:
            trend = (
                "↑析锂特征" if diff > 0.1 else "→正常" if abs(diff) < 0.05 else "↓正常"
            )
        else:  # C_low, C_kappa
            trend = (
                "↓可能异常" if diff < -0.1 else "→正常" if abs(diff) < 0.05 else "↑正常"
            )

        print(
            f"{metrics_names[metric]:>12} {normal_val:>10.3f} {lithium_val:>10.3f} {diff:>+10.3f} {trend}"
        )

    # 整体风险评估
    print("\n" + "=" * 60)
    print("风险评估")
    print("=" * 60)

    def calculate_risk_score(metrics):
        return (
            metrics["S_plat"] * 0.3
            + metrics["S_amp"] * 0.25
            + metrics["S_bump"] * 0.2
            + metrics["S_mono"] * 0.15
            + (1 - metrics["C_low"]) * 0.1
        )

    normal_risk = calculate_risk_score(normal_metrics)
    lithium_risk = calculate_risk_score(lithium_metrics)

    print(f"正常段风险评分: {normal_risk:.3f}")
    print(f"析锂段风险评分: {lithium_risk:.3f}")
    print(f"风险提升: {lithium_risk - normal_risk:+.3f}")

    # 风险等级判定
    def get_risk_level(score):
        if score > 0.7:
            return "高风险 ⚠️"
        elif score > 0.4:
            return "中等风险 ⚡"
        else:
            return "低风险 ✅"

    print(f"\n正常段: {get_risk_level(normal_risk)}")
    print(f"析锂段: {get_risk_level(lithium_risk)}")

    # 可视化（可选）
    try:
        plot_test_results(test_data, normal_metrics, lithium_metrics)
    except ImportError:
        print("\n注意: matplotlib未安装，跳过可视化")

    print("\n" + "=" * 60)
    print("测试完成！")
    print("说明: 析锂段的S_plat、S_bump、S_amp应明显高于正常段")
    print("=" * 60)

    return normal_metrics, lithium_metrics


def plot_test_results(test_data, normal_metrics, lithium_metrics):
    """绘制测试结果"""

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. 电流对比
    axes[0, 0].plot(
        test_data["t"], test_data["I_normal"], "b-", label="正常电流", alpha=0.7
    )
    axes[0, 0].plot(
        test_data["t"],
        test_data["I_with_lithium"],
        "r-",
        label="含析锂特征",
        linewidth=2,
    )
    axes[0, 0].axhline(y=2.0, color="g", linestyle="--", alpha=0.5, label="2A阈值")
    axes[0, 0].set_xlabel("时间 (s)")
    axes[0, 0].set_ylabel("电流 (A)")
    axes[0, 0].set_title("电流曲线对比")
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 软指标雷达图
    metrics_names = ["S_plat", "S_bump", "S_mono", "S_amp", "C_low", "C_kappa"]
    normal_values = [normal_metrics[m] for m in metrics_names]
    lithium_values = [lithium_metrics[m] for m in metrics_names]

    angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    normal_values += normal_values[:1]
    lithium_values += lithium_values[:1]

    axes[0, 1].plot(angles, normal_values, "b-o", label="正常段", linewidth=2)
    axes[0, 1].plot(angles, lithium_values, "r-o", label="析锂段", linewidth=2)
    axes[0, 1].fill(angles, lithium_values, "red", alpha=0.1)
    axes[0, 1].set_xticks(angles[:-1])
    axes[0, 1].set_xticklabels(metrics_names)
    axes[0, 1].set_ylim(0, 1)
    axes[0, 1].set_title("软指标雷达图")
    axes[0, 1].legend()
    axes[0, 1].grid(True)

    # 3. 指标柱状对比
    x_pos = np.arange(len(metrics_names))
    width = 0.35

    axes[1, 0].bar(
        x_pos - width / 2, normal_values[:-1], width, label="正常段", alpha=0.7
    )
    axes[1, 0].bar(
        x_pos + width / 2, lithium_values[:-1], width, label="析锂段", alpha=0.7
    )
    axes[1, 0].set_xlabel("软指标")
    axes[1, 0].set_ylabel("指标值")
    axes[1, 0].set_title("软指标数值对比")
    axes[1, 0].set_xticks(x_pos)
    axes[1, 0].set_xticklabels(metrics_names, rotation=45)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 差值分析
    differences = [lithium_metrics[m] - normal_metrics[m] for m in metrics_names]
    colors = [
        "red" if d > 0.05 else "green" if d < -0.05 else "gray" for d in differences
    ]

    axes[1, 1].bar(metrics_names, differences, color=colors, alpha=0.7)
    axes[1, 1].set_xlabel("软指标")
    axes[1, 1].set_ylabel("差值 (析锂段 - 正常段)")
    axes[1, 1].set_title("软指标变化量")
    axes[1, 1].tick_params(axis="x", rotation=45)
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color="black", linestyle="-", alpha=0.5)

    plt.tight_layout()
    plt.savefig("soft_metrics_test_results.png", dpi=300, bbox_inches="tight")
    print("\n可视化结果已保存为: soft_metrics_test_results.png")

    # 显示图形（如果在交互环境中）
    try:
        plt.show()
    except:
        pass


def test_sliding_window_fade_rate():
    """测试滑动窗口容量衰减率计算"""

    print("\n" + "=" * 60)
    print("滑动窗口容量衰减率测试")
    print("=" * 60)

    # 模拟容量数据（随循环衰减）
    cycles = list(range(1, 101))  # 100个循环
    initial_capacity = 3.0

    # 模拟三种衰减模式
    capacities_linear = {}  # 线性衰减
    capacities_nonlinear = {}  # 非线性衰减
    capacities_knee = {}  # 膝点衰减

    for cycle in cycles:
        # 线性衰减
        capacities_linear[cycle] = initial_capacity - 0.01 * cycle

        # 非线性衰减 (指数)
        capacities_nonlinear[cycle] = initial_capacity * np.exp(-0.005 * cycle)

        # 膝点衰减 (前50循环慢，后50循环快)
        if cycle <= 50:
            capacities_knee[cycle] = initial_capacity - 0.002 * cycle
        else:
            capacities_knee[cycle] = initial_capacity - 0.002 * 50 - 0.02 * (cycle - 50)

    # 测试滑动窗口计算
    config = {"physics_params": {}}
    analyzer = LithiumDetectionDataLoader(config)

    test_cases = [
        ("线性衰减", capacities_linear),
        ("非线性衰减", capacities_nonlinear),
        ("膝点衰减", capacities_knee),
    ]

    for name, capacities in test_cases:
        print(f"\n{name}:")
        print("循环    容量(Ah)  衰减率(Ah/cycle)")
        print("-" * 40)

        for cycle in [10, 30, 50, 70, 90]:
            capacity = capacities[cycle]
            fade_rate = analyzer.calculate_sliding_window_fade_rate(
                cycle, capacities, window_size=10
            )
            print(f"{cycle:3d}     {capacity:.3f}     {fade_rate:+.4f}")


if __name__ == "__main__":
    # 运行测试
    normal_metrics, lithium_metrics = test_soft_metrics_calculation()
    test_sliding_window_fade_rate()

    print("\n" + "=" * 60)
    print("全部测试完成！软指标提取功能工作正常。")
    print("=" * 60)

