# 数据处理模块使用指南

## 概述

`data_processor.py` 模块是析锂检测系统的核心处理引擎，负责CV段识别、物理软指标计算和特征工程，实现从原始数据到风险评分的完整转换流程。

## 核心类介绍

### 1. CVSegmentDetector 类

恒压(CV)阶段检测器，是系统的第一道处理环节。

#### 设计理念
基于"电压低方差 + 电流负斜率"的双重门控策略，实现鲁棒的CV段自动识别。

#### 关键函数说明

```python
def find_cv_segments_fixed(t: np.ndarray, V: np.ndarray, I: np.ndarray) -> List[CVSegment]
```
- **功能**：使用预设固定阈值检测CV段
- **输入**：时间、电压、电流序列
- **输出**：CV段对象列表，包含起止索引、置信度、持续时间
- **适用场景**：标准工况、已知电池类型

```python
def find_cv_segments_adaptive(t: np.ndarray, V: np.ndarray, I: np.ndarray) -> Tuple[List[CVSegment], Dict]
```
- **功能**：自适应阈值检测，提高跨工况鲁棒性
- **输入**：时间、电压、电流序列
- **输出**：CV段列表 + 校准参数字典
- **适用场景**：新电池类型、变化工况、生产环境

```python
def calibrate_thresholds(historical_data: List[Dict]) -> Dict
```
- **功能**：基于健康历史数据校准检测阈值
- **输入**：历史健康数据列表
- **输出**：校准后的阈值参数
- **用途**：系统初始化、定期重校准

### 2. PhysicsCalculator 类

物理软指标计算器，实现物理机理的数学量化。

#### 设计理念
将"平台/凸起/回嵌"等物理概念转化为可微的数学指标，支持端到端训练。

#### 关键函数说明

```python
def compute_soft_metrics(t: np.ndarray, I: np.ndarray, V: np.ndarray, Q: np.ndarray) -> PhysicsMetrics
```
- **功能**：计算完整的7维物理软指标
- **输入**：时间、电流、电压、容量序列
- **输出**：PhysicsMetrics对象，包含所有软指标和融合风险评分
- **核心算法**：
  - 平台度：基于电流导数的软门函数
  - 凸起度：先正后负曲率的面积乘积
  - 回嵌幅度：基线偏差与小电流权重的乘积

```python
def estimate_monotone_baseline(current: np.ndarray, alpha: float = 0.98) -> np.ndarray
```
- **功能**：估计单调衰减基线，用于回嵌检测
- **输入**：电流序列，衰减因子
- **输出**：基线电流序列
- **算法**：EWMA + 单调约束，避免将噪声误认为回嵌

```python
def compute_plating_amplitude(current: np.ndarray, baseline: np.ndarray) -> Tuple[float, np.ndarray]
```
- **功能**：计算回嵌幅度指标
- **输入**：电流序列、基线电流
- **输出**：回嵌幅度值、小电流权重序列
- **物理意义**：量化析锂现象的强度和位置

### 3. FeatureAggregator 类

特征聚合器，实现从段级到圈级的特征整合。

#### 设计理念
通过多策略聚合(max/mean/weighted)，将单圈内多个CV段的特征整合为圈级表示。

#### 关键函数说明

```python
def aggregate_cv_segments(cv_metrics: List[PhysicsMetrics]) -> Dict[str, float]
```
- **功能**：聚合单圈内多个CV段的指标
- **输入**：CV段物理指标列表
- **输出**：聚合后的圈级特征字典
- **策略**：风险取max，幅度取mean，一致性加权

```python
def compute_cycle_features(t: np.ndarray, I: np.ndarray, V: np.ndarray, Q: np.ndarray) -> Dict[str, float]
```
- **功能**：计算完整的圈级特征
- **输入**：单圈完整数据
- **输出**：圈级特征字典，包含Rcv、CE、dQ/dV等
- **应用**：批量分析、模型训练数据准备

### 4. AutoLabeler 类

自动标注器，实现从软评分到硬标签的转换。

#### 设计理念
基于无监督学习(GMM/Otsu)将连续的风险评分转换为离散标签，支持弱监督训练。

#### 关键函数说明

```python
def generate_labels(soft_scores: np.ndarray) -> Tuple[np.ndarray, Dict]
```
- **功能**：从软评分生成二值标签
- **输入**：软风险评分数组
- **输出**：二值标签 + 标注信息
- **策略**：GMM优先，Otsu兜底

## 数据结构定义

### CVSegment 数据结构
```python
@dataclass
class CVSegment:
    start_idx: int      # 起始索引
    end_idx: int        # 结束索引
    confidence: float   # 检测置信度
    duration: float     # 持续时间(秒)
```

### PhysicsMetrics 数据结构
```python
@dataclass
class PhysicsMetrics:
    S_plat: float      # 平台度 [0,1]
    S_bump: float      # 凸起度 [0,1]
    S_mono: float      # 非单调性 [0,∞)
    S_amp: float       # 回嵌幅度 [0,∞)
    Q_tilde: float     # 锂量代理 [0,∞)
    C_low: float       # 低电流一致性 [0,1]
    C_kappa: float     # 曲率一致性 [0,1]
    y_risk: float      # 融合风险评分 [0,1]
```

## 使用示例

### 端到端CV段检测与分析
```python
from data_processor import CVSegmentDetector, PhysicsCalculator

# 初始化检测器
detector = CVSegmentDetector(params={
    'v_var_th': 1e-4,
    'i_slope_th': -1e-5,
    'min_duration': 30.0
})

# 检测CV段
cv_segments = detector.find_cv_segments_adaptive(t, V, I)

# 计算物理指标
calculator = PhysicsCalculator(params={
    'sg_win': 11,
    'alpha_base': 0.98,
    'tau_slope': 0.002
})

results = []
for segment in cv_segments[0]:  # cv_segments[0] 是段列表
    # 提取段数据
    start, end = segment.start_idx, segment.end_idx
    t_seg = t[start:end+1]
    I_seg = I[start:end+1]
    V_seg = V[start:end+1]
    Q_seg = Q[start:end+1]
    
    # 计算物理指标
    metrics = calculator.compute_soft_metrics(t_seg, I_seg, V_seg, Q_seg)
    results.append(metrics)
    
    print(f"CV段 {start}-{end}: 风险评分={metrics.y_risk:.3f}")
```

### 圈级特征聚合与自动标注
```python
from data_processor import FeatureAggregator, AutoLabeler

# 特征聚合
aggregator = FeatureAggregator(params={
    'betas_cycle': [0.5, 1.0, 0.5, 0.5],  # [dCE, Rcv, Pdv, Cons]
    'bias_cycle': 0.0
})

# 处理多个循环
cycle_features = []
for cycle_data in cycles:
    features = aggregator.compute_cycle_features(
        cycle_data['t'], cycle_data['I'], 
        cycle_data['V'], cycle_data['Q']
    )
    cycle_features.append(features)

# 自动标注
labeler = AutoLabeler(method='gmm')
soft_scores = np.array([f['y_soft'] for f in cycle_features])
hard_labels, label_info = labeler.generate_labels(soft_scores)

print(f"检出高风险循环: {np.sum(hard_labels)} / {len(hard_labels)}")
print(f"标注信息: {label_info}")
```

### 自适应阈值校准
```python
# 收集健康历史数据
healthy_data = []
for healthy_cycle in healthy_cycles:
    healthy_data.append({
        't': healthy_cycle['t'],
        'V': healthy_cycle['V'], 
        'I': healthy_cycle['I']
    })

# 校准阈值
calibrated_params = detector.calibrate_thresholds(healthy_data)
print(f"校准参数: {calibrated_params}")

# 使用校准参数
detector.params.update(calibrated_params)
```

## 参数配置指导

### CV检测参数
```python
cv_params = {
    'v_var_win': 51,        # 电压方差窗口大小
    'v_var_th': 1e-4,       # 电压方差阈值
    'i_slope_th': -1e-5,    # 电流斜率阈值 
    'min_duration': 30.0,   # 最小段时长(秒)
    'v_q': 0.1,            # 自适应电压方差分位数
    'slope_q': 0.2         # 自适应斜率分位数
}
```

### 物理指标参数
```python
physics_params = {
    'sg_win': 11,           # Savitzky-Golay平滑窗口
    'tau_slope': 0.002,     # 平台斜率阈值
    'sigma_p': 0.001,       # 平台软门宽度
    'kappa_pos': 0.0005,    # 正曲率阈值
    'kappa_neg': 0.0005,    # 负曲率阈值
    'alpha_base': 0.98,     # 基线EWMA因子
    'sigma_i': 0.05,        # 小电流权重宽度
    'betas': [1.0, 1.0, 0.5, 1.0, 1.0, 0.5, 0.5],  # 融合权重
    'bias': 0.0             # 融合偏置
}
```

## 性能优化策略

### 计算优化
1. **向量化计算**：使用numpy向量操作替代循环
2. **内存管理**：及时释放大型中间变量
3. **并行处理**：多进程处理独立的CV段

### 精度优化
1. **平滑预处理**：使用Savitzky-Golay滤波降噪
2. **自适应参数**：根据数据特性动态调整阈值
3. **多尺度融合**：结合不同时间窗口的结果

## 常见问题解决

### CV段检测问题
**问题**：检测不到CV段或误检
**解决**：
- 检查电压方差阈值是否过严
- 验证电流斜率阈值设置
- 考虑使用自适应阈值方法

### 物理指标异常
**问题**：指标值超出预期范围
**解决**：
- 检查输入数据质量
- 验证基线估计是否合理
- 调整软门函数的宽度参数

### 标注质量问题
**问题**：自动标注结果不理想
**解决**：
- 增加训练样本数量
- 调整GMM组件数或使用不同分割方法
- 引入专家知识进行后处理

## 算法原理简介

### 物理软指标数学表达

1. **平台度指标**：
   $$S_{plat} = \frac{1}{N}\sum_i \sigma\left(\frac{\tau - |i'(s_i)|}{\sigma_p}\right)$$

2. **凸起度指标**：
   $$S_{bump} = \sigma\left(\frac{A_+ \cdot A_- - \eta}{\sigma_b}\right)$$

3. **回嵌幅度指标**：
   $$S_{amp} = \frac{1}{N}\sum_i w_{low}(s_i) \cdot \delta i^+(s_i)$$

### 风险融合公式
$$y_{risk} = \sigma(\boldsymbol{\beta}^T \boldsymbol{s} + b)$$

其中 $\boldsymbol{s} = [S_{plat}, S_{bump}, S_{mono}, S_{amp}, Q_{tilde}, C_{low}, C_{\kappa}]^T$

## 扩展开发指南

模块采用插件式架构，支持：
- 新的CV段检测算法
- 自定义物理指标计算
- 不同的特征聚合策略
- 多样化的自动标注方法

开发者可以通过继承基类并实现相应接口来扩展功能。
