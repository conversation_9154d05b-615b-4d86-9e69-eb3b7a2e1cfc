#!/usr/bin/env python3
"""
Soft Metrics Analyzer - 电池析锂检测软指标分析器（自包含版本）
基于CV阶段物理特性的智能析锂检测系统，采用自适应电流阈值和物理采样条件
生成27列统一格式的综合分析报告，包含7个核心软指标、电池健康状态评估和智能析锂诊断

核心特性：
- 灵活阈值设置：默认自动计算(Icv_max/3)，可手动指定具体数值
- 智能采样：大于阈值(时间≥3s OR 电流变化>Icv_max/50)，小于阈值(时间≥3s)
- 物理约束：基于CV阶段电流衰减特性的数据筛选
- 所有必要的功能已直接集成，无需外部依赖文件
"""

# ==================================================================================
# 使用说明 - Usage Instructions
# ==================================================================================

# 📁 单个文件处理 - Single File Processing
# python soft_metrics_analyzer.py "path/to/battery_data.json"
# python soft_metrics_analyzer.py "E:\data\3C_battery-1_CV_Qcha_CE.json"

# 📂 文件夹批量处理 - Batch Processing
# python soft_metrics_analyzer.py "/path/to/json/folder"
# python soft_metrics_analyzer.py "E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\XJTU_rawdata_mat"

# ⚙️ 完整参数示例 - Complete Parameter Examples
# python soft_metrics_analyzer.py input_path -o ./results -w 12
# python soft_metrics_analyzer.py "battery_data.json" --output ./analysis_results --window-size 15
# python soft_metrics_analyzer.py "battery_data.json" -c 2.0  # 手动指定2.0A阈值
# python soft_metrics_analyzer.py "battery_data.json"         # 自动计算阈值(Icv_max/3)

# 🔍 启用/禁用析锂诊断 - Enable/Disable Lithium Plating Diagnosis
# python soft_metrics_analyzer.py "data.json"                    # 默认启用诊断 (Default: diagnosis enabled)
# python soft_metrics_analyzer.py "data.json" --no-diagnosis     # 禁用诊断，仅基础分析 (Basic analysis only)

# 📊 输出文件说明 - Output Files Description
# 默认输出 (Default Output):
# - {filename}_analysis.csv        : 27列综合分析 (24列基础 + 3列诊断)
# - {filename}_lithium_diagnosis.json : 详细析锂诊断报告 (JSON格式)
#
# 禁用诊断时 (With --no-diagnosis):
# - {filename}_analysis.csv        : 24列基础分析

# ==================================================================================

import sys
import argparse
import pandas as pd
import numpy as np
import os
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from scipy.signal import savgol_filter

# 尝试导入析锂诊断模块
try:
    from lithium_plating_diagnostic import (
        LithiumPlatingDiagnostic,
        save_diagnosis_report,
    )

    DIAGNOSTIC_AVAILABLE = True
except ImportError:
    DIAGNOSTIC_AVAILABLE = False
    print("注意：析锂诊断模块未找到，将使用基础分析功能")


# ======================
# 核心数据处理功能（自包含实现）
# ======================


def log(message):
    """记录日志消息"""
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}")
    logging.info(message)


def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """
    计算dI/dt，使用累积采样方法提高性能

    改进点：
    - 使用累积采样而非相邻点比较
    - 适应实际数据中相邻点时间间隔约1s的情况
    - 确保在CV阶段能有效采样到足够的dI/dt数据点
    """
    if len(time_s) < 2:
        return None, None

    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # 使用累积采样策略生成有效的采样点
        valid_indices = compute_cumulative_sampling_indices(
            time_array, current_array, min_time_interval=3.0
        )

        if len(valid_indices) < 2:
            log("Warning: 累积采样后数据点不足，无法计算dI/dt")
            return None, None

        # 基于采样点计算dI/dt
        sampled_times = time_array[valid_indices]
        sampled_currents = current_array[valid_indices]

        # 计算采样点之间的差值
        dt = np.diff(sampled_times)
        dI = np.diff(sampled_currents)

        # 计算dI/dt
        dIdt_values = dI / dt
        didt_times = sampled_times[:-1]  # 对应dI/dt的时间点

        # Fix: Ensure we're not returning NaN or infinite values
        valid_values = ~np.isnan(dIdt_values) & ~np.isinf(dIdt_values)
        if not np.all(valid_values):
            dIdt_values = dIdt_values[valid_values]
            didt_times = didt_times[valid_values]

        if len(dIdt_values) == 0:
            return None, None

        log(
            f"    dI/dt计算: 原始点数={len(time_array)}, 采样点数={len(valid_indices)}, dI/dt点数={len(dIdt_values)}"
        )

        # Savitzky-Golay平滑处理 - 默认禁用，可根据需要启用
        # 如需启用平滑处理以减少噪声，请取消下面代码的注释：
        # if len(dIdt_values) > 5:
        #     window_size = min(5, len(dIdt_values) - (len(dIdt_values) % 2 == 0))
        #     if window_size >= 3:
        #         try:
        #             dIdt_values = savgol_filter(dIdt_values, window_size, 2)
        #         except Exception:
        #             pass  # 如果平滑失败，使用原始值
        # 说明：window_size为平滑窗口大小(建议3-7)，2为多项式阶数
        # 平滑可以减少高频噪声，但可能会削弱析锂信号的尖锐特征

        # 确保返回的是numpy数组而不是视图
        return np.array(dIdt_values), np.array(didt_times)
    except Exception as e:
        log(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None


def compute_cumulative_sampling_indices(
    time_array, current_array, min_time_interval=3.0
):
    """
    计算累积采样的有效索引

    参数:
    - time_array: 时间数组
    - current_array: 电流数组
    - min_time_interval: 最小累积时间间隔(秒)

    返回:
    - valid_indices: 有效采样点的索引数组
    """
    if len(time_array) < 2:
        return np.array([])

    valid_indices = [0]  # 第一个点总是包含
    last_sampled_idx = 0

    for i in range(1, len(time_array)):
        # 计算与上一个采样点的累积时间差
        cumulative_time_diff = time_array[i] - time_array[last_sampled_idx]

        # 如果累积时间间隔达到阈值，则采样该点
        if cumulative_time_diff >= min_time_interval:
            valid_indices.append(i)
            last_sampled_idx = i

    return np.array(valid_indices)


def adaptive_downsample_data(data, downsample_params):
    """
    自适应降采样算法
    """
    if isinstance(downsample_params, dict):
        # Adaptive downsampling based on current magnitude
        i_threshold = downsample_params.get("i_threshold", 0.5)
        factor_high = downsample_params.get(
            "factor_high", 1
        )  # For |current| > threshold
        factor_low = downsample_params.get(
            "factor_low", 1
        )  # For |current| <= threshold

        # Split data based on current threshold
        current_abs = np.abs(data["Current(A)"])
        high_current_mask = current_abs > i_threshold
        low_current_mask = current_abs <= i_threshold

        # Apply different downsampling factors
        processed_data_parts = []

        if high_current_mask.any() and factor_high > 1:
            high_current_data = data[high_current_mask]
            high_indices = np.arange(0, len(high_current_data), factor_high)
            if len(high_indices) > 0:
                high_current_data = high_current_data.iloc[high_indices].copy()
            processed_data_parts.append(high_current_data)
        elif high_current_mask.any():
            processed_data_parts.append(data[high_current_mask])

        if low_current_mask.any() and factor_low > 1:
            low_current_data = data[low_current_mask]
            low_indices = np.arange(0, len(low_current_data), factor_low)
            if len(low_indices) > 0:
                low_current_data = low_current_data.iloc[low_indices].copy()
            processed_data_parts.append(low_current_data)
        elif low_current_mask.any():
            processed_data_parts.append(data[low_current_mask])

        # Combine and sort by time
        if processed_data_parts:
            data = pd.concat(processed_data_parts).sort_values(by="Time(s)")
    else:
        # Traditional uniform downsampling
        downsample_factor = downsample_params
        if downsample_factor > 1:
            indices = np.arange(0, len(data), downsample_factor)
            if len(indices) > 0:
                data = data.iloc[indices].copy()

    return data


def extract_cycles_from_json_integrated(json_file_path):
    """
    从JSON文件提取循环数据
    """
    log(f"Loading JSON file: {json_file_path}")

    try:
        with open(json_file_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except Exception as e:
        log(f"Error loading JSON file: {str(e)}")
        return None, None, None

    log("Extracting cycle data from JSON...")

    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}

    # Verify data structure
    if not isinstance(json_data, dict):
        log("Error: Invalid JSON data format - expected a dictionary")
        return None, None, None

    # Extract cycle data from JSON
    for key in json_data.keys():
        if key.startswith("Cycle_"):
            try:
                cycle_num = int(key.split("_")[1])
                cycle_data = json_data[key]

                # Verify required fields exist
                if not isinstance(cycle_data, dict):
                    log(f"  Warning: Invalid data format for cycle {key}")
                    continue

                # Get time data and current data
                time_min = cycle_data.get("relative_time_min", [])
                current_A = cycle_data.get("current_A", [])

                if not time_min or not current_A:
                    log(f"  Warning: Missing time or current data for cycle {key}")
                    continue

                # Check arrays have same length
                if len(time_min) != len(current_A):
                    log(
                        f"  Warning: Time and current arrays have different lengths in cycle {key}"
                    )
                    # Use the shorter length
                    min_len = min(len(time_min), len(current_A))
                    time_min = time_min[:min_len]
                    current_A = current_A[:min_len]

                if len(time_min) > 0 and len(current_A) > 0:
                    # 确保正确转换时间单位：从分钟转换为秒
                    # 首先归一化时间（从0开始）
                    start_time = time_min[0]
                    # 然后将分钟转换为秒
                    time_s = [(t - start_time) * 60 for t in time_min]

                    # Create dataframe with standard format
                    df = pd.DataFrame(
                        {
                            "Time(s)": time_s,
                            "Current(A)": current_A,
                            "Time(min)": [t - start_time for t in time_min],
                        }
                    )

                    all_cycles_data[cycle_num] = df
                    log(
                        f"  Extracted cycle {cycle_num}, containing {len(df)} data points"
                    )
            except (ValueError, IndexError, TypeError) as e:
                log(f"  Error processing cycle {key}: {str(e)}")
                continue

    # Extract discharge capacity data (if exists)
    if "Discharge Capacity" in json_data:
        try:
            discharge_capacities = json_data["Discharge Capacity"]
            if isinstance(discharge_capacities, list):
                for i, capacity in enumerate(discharge_capacities):
                    cycle_num = i + 1
                    if cycle_num in all_cycles_data:
                        try:
                            capacity_value = float(capacity)
                            cycle_capacities[cycle_num] = capacity_value
                            log(
                                f"  Extracted capacity for cycle {cycle_num}: {capacity_value:.4f} Ah"
                            )
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric capacity value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'Discharge Capacity' is not a list")
        except Exception as e:
            log(f"  Error processing capacity data: {str(e)}")
    else:
        log(f"  Note: 'Discharge Capacity' data not found in JSON")

    # Extract CE data (if exists)
    if "CE" in json_data:
        try:
            ce_values = json_data["CE"]
            if isinstance(ce_values, list):
                for i, ce in enumerate(ce_values):
                    cycle_num = i + 1
                    if cycle_num in all_cycles_data:
                        try:
                            ce_value = float(ce)
                            cycle_ce_values[cycle_num] = ce_value
                            log(f"  Extracted CE for cycle {cycle_num}: {ce_value:.6f}")
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric CE value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'CE' is not a list")
        except Exception as e:
            log(f"  Error processing CE data: {str(e)}")
    else:
        log(f"  Note: 'CE' data not found in JSON")

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found")
    return all_cycles_data, cycle_capacities, cycle_ce_values


class LithiumDetectionDataLoader:
    """专为析锂检测设计的电池数据加载器"""

    def __init__(self, config: Dict, manual_threshold: Optional[float] = None):
        """
        初始化析锂检测数据加载器

        Args:
            config: 配置字典
            manual_threshold: 手动指定的电流阈值，None则自动计算为Icv_max/3
        """
        self.config = config
        self.sampling_rate = config.get("sampling_rate", 1.0)
        self.manual_threshold = manual_threshold

        # 物理软指标参数
        self.physics_params = config.get(
            "physics_params",
            {
                "tau_slope": 0.002,
                "kappa_pos": 0.0005,
                "kappa_neg": -0.0005,
                "alpha_baseline": 0.98,
                "sigma_p": 0.001,
                "sigma_b": 0.001,
                "i_threshold": 0.1,
            },
        )

        # 自适应降采样参数
        self.downsample_params = config.get(
            "downsample_params",
            {
                "i_threshold": 0.5,  # 电流阈值(A)
                "factor_high": 1,  # 高电流区域降采样因子
                "factor_low": 1,  # 低电流区域降采样因子（析锂敏感区）
            },
        )

        # 数据质量统计
        self.data_stats = {
            "total_files_processed": 0,
            "total_cycles_extracted": 0,
            "avg_points_per_cycle": 0,
            "data_quality_score": 0.0,
        }

    def extract_two_stage_segments(
        self, cycle_data: pd.DataFrame, current_threshold: float = None
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        将一个循环划分为两个连续段：大电流段和小电流段

        流程：
        1. 确定电流分界线（手动指定 或 Imax/3）
        2. 找到第一个电流<分界线的点作为分界点
        3. 划分为两个连续段：
           - 大电流段：循环起点 → 分界点前一个点
           - 小电流段：分界点 → 循环结束

        返回：
        - (大电流段起止索引, 小电流段起止索引)
        - 如果没有找到分界点，返回 (None, None)
        """
        current_values = cycle_data["Current(A)"].values

        # 阈值选择 - 手动指定优先，否则自动计算
        if current_threshold is None:
            current_threshold = self._calculate_optimal_threshold(current_values)
            max_i = np.max(np.abs(current_values))
            min_i = np.min(np.abs(current_values))
            log(
                f"    自动阈值计算: {current_threshold:.2f}A (Icv_max/3 = {max_i:.2f}/3, 电流范围: {max_i:.2f}A → {min_i:.3f}A)"
            )
        else:
            max_i = np.max(np.abs(current_values))
            min_i = np.min(np.abs(current_values))
            log(
                f"    手动指定阈值: {current_threshold:.2f}A (电流范围: {max_i:.2f}A → {min_i:.3f}A)"
            )

        # 找到第一个电流<分界线的点
        low_current_mask = np.abs(current_values) < current_threshold

        if not np.any(low_current_mask):
            log(f"    警告：未找到电流<{current_threshold:.2f}A的点，无法划分段")
            return None, None

        # 找到第一个低电流点的索引
        boundary_idx = np.where(low_current_mask)[0][0]

        if boundary_idx == 0:
            log(f"    警告：第一个点就是低电流，无大电流段")
            return None, None

        # 划分两个连续段
        high_current_segment = (0, boundary_idx)  # 大电流段：起点到分界点前
        low_current_segment = (
            boundary_idx,
            len(current_values),
        )  # 小电流段：分界点到结束

        # 记录段划分结果
        high_points = boundary_idx
        low_points = len(current_values) - boundary_idx
        high_current_range = (
            f"{current_values[0]:.2f}A - {current_values[boundary_idx-1]:.2f}A"
        )
        low_current_range = (
            f"{current_values[boundary_idx]:.2f}A - {current_values[-1]:.2f}A"
        )

        log(f"    段划分结果:")
        log(
            f"      大电流段: 索引0-{boundary_idx-1} ({high_points}点), 电流范围: {high_current_range}"
        )
        log(
            f"      小电流段: 索引{boundary_idx}-{len(current_values)-1} ({low_points}点), 电流范围: {low_current_range}"
        )

        return high_current_segment, low_current_segment

    def sample_high_current_segment(
        self, cycle_data: pd.DataFrame, segment_indices: Tuple[int, int]
    ) -> np.ndarray:
        """
        大电流段采样：累积时间≥3s OR 累积电流差≥1/50×Imax

        采样规则：
        - 第一个点总是采样
        - 从第一个点开始，找下一个满足条件的点：
          * 累积时间间隔≥3s OR
          * 累积电流差≥1/50×Imax
        """
        start_idx, end_idx = segment_indices
        time_values = cycle_data["Time(s)"].iloc[start_idx:end_idx].values
        current_values = cycle_data["Current(A)"].iloc[start_idx:end_idx].values

        if len(time_values) < 2:
            return np.array([0])  # 只有一个点，返回索引0

        # 计算电流变化阈值：1/50 * Imax
        Imax = np.max(np.abs(current_values))
        current_change_threshold = Imax / 50.0

        valid_indices = [0]  # 第一个点总是采样
        last_sampled_idx = 0

        time_samples = 0
        current_samples = 0

        for i in range(1, len(time_values)):
            # 计算与上一个采样点的累积差异
            cumulative_time_diff = time_values[i] - time_values[last_sampled_idx]
            cumulative_current_diff = abs(
                current_values[i] - current_values[last_sampled_idx]
            )

            # 检查采样条件
            time_condition = cumulative_time_diff >= 3.0
            current_condition = cumulative_current_diff >= current_change_threshold

            if time_condition or current_condition:
                valid_indices.append(i)
                last_sampled_idx = i

                if time_condition:
                    time_samples += 1
                if current_condition:
                    current_samples += 1

        log(
            f"      大电流段采样: 原始{len(time_values)}点 → 采样{len(valid_indices)}点 ({len(valid_indices)/len(time_values)*100:.1f}%)"
        )
        log(
            f"        时间条件采样: {time_samples}点, 电流条件采样: {current_samples}点"
        )
        log(
            f"        电流变化阈值: {current_change_threshold:.3f}A (Imax/50 = {Imax:.2f}/50)"
        )

        return np.array(valid_indices)

    def sample_low_current_segment(
        self, cycle_data: pd.DataFrame, segment_indices: Tuple[int, int]
    ) -> np.ndarray:
        """
        小电流段采样：当前采样点与上一个采样点的时间间隔≥3s

        采样规则：
        - 第一个点总是采样
        - 后续点：与上一个采样点的时间间隔≥3s
        """
        start_idx, end_idx = segment_indices
        time_values = cycle_data["Time(s)"].iloc[start_idx:end_idx].values
        current_values = cycle_data["Current(A)"].iloc[start_idx:end_idx].values

        if len(time_values) < 2:
            return np.array([0])  # 只有一个点，返回索引0

        valid_indices = [0]  # 第一个点总是采样
        last_sampled_idx = 0

        for i in range(1, len(time_values)):
            # 计算与上一个采样点的时间间隔
            time_diff = time_values[i] - time_values[last_sampled_idx]

            if time_diff >= 3.0:
                valid_indices.append(i)
                last_sampled_idx = i

        log(
            f"      小电流段采样: 原始{len(time_values)}点 → 采样{len(valid_indices)}点 ({len(valid_indices)/len(time_values)*100:.1f}%)"
        )

        return np.array(valid_indices)

    def _extract_segments_with_sampling_conditions(
        self, cycle_data: pd.DataFrame, current_threshold: float
    ) -> List[Tuple[int, int]]:
        """
        基于物理特性的采样条件进行段提取

        采样条件:
        - 大于阈值部分: 时间间隔≥3s OR 电流变化>Icv_max/50
        - 小于阈值部分: 时间间隔≥3s

        注意: 当前已注释采样条件，直接使用所有数据点进行计算
        """
        current_values = cycle_data["Current(A)"].values
        time_values = cycle_data["Time(s)"].values

        # Icv_max: 当前循环CV阶段的最大电流值
        Icv_max = np.max(np.abs(current_values))
        current_change_threshold = Icv_max / 50.0  # 电流变化阈值

        # ======== 使用改进的两阶段采样条件 ========
        # 生成改进的采样掩码
        sampling_mask = self._generate_two_stage_sampling_mask(
            current_values, time_values, current_threshold, current_change_threshold
        )

        # 基于采样掩码找到低电流连续段
        low_current_mask = np.abs(current_values) < current_threshold
        # 修复：低电流段提取不应该受到采样条件限制
        # 采样条件用于后续的软指标计算，而不是段的识别
        valid_low_current_mask = low_current_mask  # 直接使用低电流掩码

        # 记录采样效果
        total_points = len(current_values)
        low_current_points = np.sum(low_current_mask)
        sampled_points = np.sum(sampling_mask)
        valid_points = np.sum(valid_low_current_mask)

        # 添加时间间隔统计
        time_intervals = np.diff(time_values)
        log(
            f"    时间间隔统计: 最小={np.min(time_intervals):.3f}s, "
            f"最大={np.max(time_intervals):.3f}s, 平均={np.mean(time_intervals):.3f}s"
        )
        log(
            f"    数据统计: 总点数={total_points}, 低电流点={low_current_points}, "
            f"采样点={sampled_points}, 有效点={valid_points} [两阶段采样]"
        )
        log(
            f"    电流变化阈值: {current_change_threshold:.3f}A (Icv_max/50 = {Icv_max:.2f}/50)"
        )

        # 找到连续段
        segments = []
        in_segment = False
        start_idx = 0
        min_points = self._calculate_min_segment_points(
            current_values, current_threshold
        )

        for i, is_valid_low_current in enumerate(valid_low_current_mask):
            if is_valid_low_current and not in_segment:
                start_idx = i
                in_segment = True
            elif not is_valid_low_current and in_segment:
                if i - start_idx >= min_points:
                    segments.append((start_idx, i))
                in_segment = False

        # 处理末尾情况
        if in_segment and len(valid_low_current_mask) - start_idx >= min_points:
            segments.append((start_idx, len(valid_low_current_mask)))

        return segments

    def _generate_sampling_mask(
        self,
        current_values: np.ndarray,
        time_values: np.ndarray,
        current_threshold: float,
        current_change_threshold: float,
    ) -> np.ndarray:
        """
        生成基于物理条件的采样掩码

        采样条件:
        - 大于阈值部分: 时间间隔≥3s OR 电流变化>Icv_max/50
        - 小于阈值部分: 时间间隔≥3s
        """
        mask = np.zeros(len(current_values), dtype=bool)
        mask[0] = True  # 第一个点总是包含

        for i in range(1, len(current_values)):
            time_diff = time_values[i] - time_values[i - 1]
            current_diff = abs(current_values[i] - current_values[i - 1])
            current_abs = abs(current_values[i])

            if current_abs >= current_threshold:
                # 大于阈值部分: 时间间隔≥3s OR 电流变化>Icv_max/50
                if time_diff >= 3.0 or current_diff > current_change_threshold:
                    mask[i] = True
            else:
                # 小于阈值部分: 时间间隔≥3s
                if time_diff >= 3.0:
                    mask[i] = True

        return mask

    def _generate_two_stage_sampling_mask(
        self,
        current_values: np.ndarray,
        time_values: np.ndarray,
        current_threshold: float,
        current_change_threshold: float,
    ) -> np.ndarray:
        """
        生成基于两阶段CV特性的累积采样掩码

        采样策略（累积模式）:
        1. 大电流阶段 (|I| ≥ Imax/3, 恒压阶段前期):
           - 第一个点必须采样
           - 后续点满足: 累积时间间隔≥3s OR 累积电流变化≥Imax/50

        2. 小电流阶段 (|I| < Imax/3, 恒压阶段后期):
           - 满足: 累积时间间隔≥3s

        参数:
        - current_threshold: 电流阈值 (Imax/3, 默认约2A)
        - current_change_threshold: 电流变化阈值 (Imax/50)

        注意：这是累积采样，与上一个采样点比较，而非相邻点比较
        """
        mask = np.zeros(len(current_values), dtype=bool)
        mask[0] = True  # 第一个点总是包含

        # 统计计数器
        high_current_time_samples = 0
        high_current_change_samples = 0
        low_current_time_samples = 0

        # 累积采样：跟踪上一个被采样的点
        last_sampled_idx = 0

        for i in range(1, len(current_values)):
            # 计算与上一个采样点的累积差异
            cumulative_time_diff = time_values[i] - time_values[last_sampled_idx]
            cumulative_current_diff = abs(
                current_values[i] - current_values[last_sampled_idx]
            )
            current_abs = abs(current_values[i])

            should_sample = False
            sample_reason = ""

            if current_abs >= current_threshold:
                # 大电流阶段 (恒压阶段前期): 累积时间间隔≥3s OR 累积电流变化≥Imax/50
                if cumulative_time_diff >= 3.0:
                    should_sample = True
                    sample_reason = "high_current_time"
                    high_current_time_samples += 1
                elif cumulative_current_diff >= current_change_threshold:
                    should_sample = True
                    sample_reason = "high_current_change"
                    high_current_change_samples += 1
            else:
                # 小电流阶段 (恒压阶段后期): 累积时间间隔≥3s
                if cumulative_time_diff >= 3.0:
                    should_sample = True
                    sample_reason = "low_current_time"
                    low_current_time_samples += 1

            if should_sample:
                mask[i] = True
                last_sampled_idx = i  # 更新最后采样点索引

        # 记录采样效果
        total_samples = np.sum(mask)
        high_current_points = np.sum(np.abs(current_values) >= current_threshold)
        low_current_points = np.sum(np.abs(current_values) < current_threshold)

        log(f"    累积两阶段采样统计:")
        log(f"      大电流阶段 (≥{current_threshold:.2f}A): {high_current_points}点")
        log(f"        - 累积时间条件采样: {high_current_time_samples}点")
        log(f"        - 累积电流变化采样: {high_current_change_samples}点")
        log(f"      小电流阶段 (<{current_threshold:.2f}A): {low_current_points}点")
        log(f"        - 累积时间条件采样: {low_current_time_samples}点")
        log(f"      总采样点数: {total_samples}点 (含首点)")
        log(f"      采样率: {total_samples/len(current_values)*100:.1f}%")

        return mask

    def _calculate_optimal_threshold(self, current_values: np.ndarray) -> float:
        """
        基于CV阶段物理特性计算最优电流阈值

        物理原理:
        - CV阶段电流从大到小指数衰减
        - 电流阈值界限为最大电流的1/3
        - Icv_max为当前循环CV阶段的最大电流值
        """
        # Icv_max: 当前循环CV阶段的最大电流值
        Icv_max = np.max(np.abs(current_values))

        # 基于CV阶段电流分布的物理模型: 阈值 = Icv_max / 3
        optimal_threshold = Icv_max / 3.0

        return float(optimal_threshold)

    def _calculate_min_segment_points(
        self, current_values: np.ndarray, threshold: float
    ) -> int:
        """
        基于数据密度和物理特性计算最小段点数

        采样条件:
        - 大于阈值部分: 时间间隔≥3s OR 电流变化>Icv_max/50
        - 小于阈值部分: 时间间隔≥3s
        - Icv_max为当前循环CV阶段的最大电流值
        """
        # 基础最小点数 - 简化为固定值，实际采样由时间和电流条件控制
        return 5  # 降低最小要求，让时间间隔条件起主导作用

    def compute_seven_soft_metrics(
        self, segment_data: Dict[str, np.ndarray]
    ) -> Dict[str, float]:
        """
        计算7个软指标
        """
        try:
            t = segment_data["t"]
            I = segment_data["I"]
            V = segment_data["V"] if "V" in segment_data else None
            Q = segment_data["Q"] if "Q" in segment_data else None

            # 数据预处理和平滑
            I_smooth = self._smooth_current_data(I, t)

            # 计算一阶和二阶导数
            dI_dt = np.gradient(I_smooth, t)
            d2I_dt2 = np.gradient(dI_dt, t)

            # 基线估计（指数衰减拟合）
            I_baseline = self._estimate_exponential_baseline(I_smooth, t)

            # 计算7个软指标
            S_plat = self._compute_platform_degree(dI_dt)
            S_bump = self._compute_bump_degree(d2I_dt2)
            S_mono = self._compute_non_monotonicity(I_smooth, I_baseline)
            S_amp = self._compute_reintercalation_amplitude(I_smooth, I_baseline, t)
            Q_tilde = self._compute_lithium_proxy(Q, I_smooth) if Q is not None else 0.0
            C_low = self._compute_low_current_consistency(I_smooth, t)
            C_kappa = self._compute_curvature_consistency(d2I_dt2)

            return {
                "S_plat": float(S_plat),
                "S_bump": float(S_bump),
                "S_mono": float(S_mono),
                "S_amp": float(S_amp),
                "Q_tilde": float(Q_tilde),
                "C_low": float(C_low),
                "C_kappa": float(C_kappa),
            }

        except Exception as e:
            log(f"Error computing soft metrics: {str(e)}")
            return {
                "S_plat": 0.0,
                "S_bump": 0.0,
                "S_mono": 0.0,
                "S_amp": 0.0,
                "Q_tilde": 0.0,
                "C_low": 0.0,
                "C_kappa": 0.0,
            }

    def _smooth_current_data(self, current: np.ndarray, time: np.ndarray) -> np.ndarray:
        """使用Savitzky-Golay滤波平滑电流数据"""
        if len(current) > 5:
            window_size = min(5, len(current) - (len(current) % 2 == 0))
            if window_size >= 3:
                try:
                    return savgol_filter(current, window_size, 2)
                except Exception:
                    pass
        return current

    def _estimate_exponential_baseline(
        self, current: np.ndarray, time: np.ndarray
    ) -> np.ndarray:
        """估计指数衰减基线"""
        try:
            # 简单的指数衰减拟合：I = I0 * exp(-t/tau)
            if len(current) < 3:
                return current

            # 取对数进行线性拟合
            I_positive = np.maximum(current, 1e-6)  # 避免取对数时出现负数
            log_I = np.log(I_positive)

            # 线性拟合 log(I) = log(I0) - t/tau
            coeffs = np.polyfit(time, log_I, 1)
            baseline = np.exp(coeffs[1] + coeffs[0] * time)

            return baseline
        except Exception:
            # 如果拟合失败，使用EWMA作为备选
            alpha = self.physics_params["alpha_baseline"]
            baseline = np.zeros_like(current)
            baseline[0] = current[0]
            for i in range(1, len(current)):
                baseline[i] = alpha * baseline[i - 1] + (1 - alpha) * current[i]
            return baseline

    def _compute_platform_degree(self, dI_dt: np.ndarray) -> float:
        """
        计算平台度指标 S_plat
        物理意义: CV阶段析锂时电流斜率接近零的持续性
        """
        tau = self.physics_params["tau_slope"]
        sigma_p = self.physics_params["sigma_p"]

        # 软平台指标：斜率接近零的持续性
        # 在CV阶段，正常情况下dI/dt应该单调递减
        # 析锂时会出现dI/dt接近零的平台现象
        platform_scores = 1.0 / (1.0 + np.exp(-(tau - np.abs(dI_dt)) / sigma_p))

        # 加权计算：CV后期的平台更重要（析锂更明显）
        time_weights = np.linspace(0.5, 1.0, len(platform_scores))
        weighted_score = np.average(platform_scores, weights=time_weights)

        return float(weighted_score)

    def _compute_bump_degree(self, d2I_dt2: np.ndarray) -> float:
        """计算凸起度指标 S_bump"""
        kappa_pos = self.physics_params["kappa_pos"]
        kappa_neg = self.physics_params["kappa_neg"]
        sigma_b = self.physics_params["sigma_b"]
        eta = 0.001

        # 正负曲率区域
        A_pos = np.sum(np.maximum(0, d2I_dt2 - kappa_pos))
        A_neg = np.sum(np.maximum(0, -kappa_neg - d2I_dt2))

        # 凸起指标
        bump_product = A_pos * A_neg - eta
        return 1.0 / (1.0 + np.exp(-bump_product / sigma_b))

    def _compute_non_monotonicity(
        self, current: np.ndarray, baseline: np.ndarray
    ) -> float:
        """计算非单调性指标 S_mono"""
        # 计算相对于基线的偏差
        deviation = current - baseline

        # 检测上升段（偏离单调衰减）
        positive_deviations = np.maximum(0, deviation)
        total_deviation = np.sum(np.abs(deviation))

        if total_deviation > 0:
            return np.sum(positive_deviations) / total_deviation
        return 0.0

    def _compute_reintercalation_amplitude(
        self, current: np.ndarray, baseline: np.ndarray, time: np.ndarray
    ) -> float:
        """计算回嵌幅度指标 S_amp"""
        i_threshold = self.physics_params["i_threshold"]
        sigma_i = 0.05

        # 计算正向偏差
        delta_I_pos = np.maximum(0, current - baseline)

        # 低电流权重
        w_low = 1.0 / (1.0 + np.exp((np.abs(current) - i_threshold) / sigma_i))

        # 加权平均
        weighted_sum = np.sum(w_low * delta_I_pos)
        total_weight = np.sum(w_low)

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def _compute_lithium_proxy(self, charge: np.ndarray, current: np.ndarray) -> float:
        """计算锂量代理指标 Q_tilde"""
        if charge is None or len(charge) == 0:
            return 0.0

        # 基于累积电荷量的特征
        total_charge = np.max(charge) - np.min(charge)

        # 电流积分的相关性
        current_integral = np.trapz(np.abs(current))

        # 归一化的锂量代理
        return total_charge / (1.0 + current_integral) if current_integral > 0 else 0.0

    def _compute_low_current_consistency(
        self, current: np.ndarray, time: np.ndarray
    ) -> float:
        """计算低电流一致性指标 C_low"""
        i_threshold = self.physics_params["i_threshold"]

        # 找到低电流区域
        low_current_mask = np.abs(current) < i_threshold

        if not np.any(low_current_mask):
            return 1.0  # 如果没有低电流区域，认为是一致的

        low_current_values = current[low_current_mask]

        # 计算变异系数（标准差/均值）
        if len(low_current_values) > 1 and np.mean(np.abs(low_current_values)) > 0:
            cv = np.std(low_current_values) / np.mean(np.abs(low_current_values))
            return 1.0 / (1.0 + cv)  # 转换为一致性指标

        return 1.0

    def _compute_curvature_consistency(self, d2I_dt2: np.ndarray) -> float:
        """计算曲率一致性指标 C_kappa"""
        if len(d2I_dt2) < 2:
            return 1.0

        # 计算曲率的变化率
        curvature_changes = np.abs(np.diff(d2I_dt2))

        # 平滑性指标（变化越小越平滑）
        mean_change = np.mean(curvature_changes)
        consistency = 1.0 / (1.0 + mean_change * 1000)  # 缩放因子

        return consistency

    def calculate_sliding_window_fade_rate(
        self, cycle_num: int, cycle_capacities: Dict[int, float], window_size: int = 10
    ) -> float:
        """
        计算滑动窗口容量衰减率
        """
        # 获取当前循环周围的数据
        available_cycles = sorted(
            [c for c in cycle_capacities.keys() if c <= cycle_num]
        )

        if len(available_cycles) < 2:
            return 0.0

        # 选择滑动窗口
        start_idx = max(0, len(available_cycles) - window_size)
        window_cycles = available_cycles[start_idx:]
        window_capacities = [cycle_capacities[c] for c in window_cycles]

        if len(window_cycles) < 2:
            return 0.0

        # 线性拟合计算衰减率
        try:
            coeffs = np.polyfit(window_cycles, window_capacities, 1)
            fade_rate = coeffs[0]  # 斜率即为衰减率
            return float(fade_rate)
        except Exception:
            # 如果拟合失败，使用简单的差值方法
            return (window_capacities[-1] - window_capacities[0]) / (
                window_cycles[-1] - window_cycles[0]
            )

    def analyze_all_cycles_for_lithium_detection(
        self, json_file_path: str, output_dir: str = "./output"
    ) -> Dict[str, str]:
        """
        分析所有循环的软指标、容量和CE数据，输出为CSV文件
        """
        import os
        from pathlib import Path

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 加载数据
        log("开始加载和分析数据...")
        all_cycles_data, cycle_capacities, cycle_ce_values = (
            extract_cycles_from_json_integrated(json_file_path)
        )

        if all_cycles_data is None:
            raise Exception("JSON数据提取失败")

        # 更新统计信息
        self.data_stats["total_files_processed"] += 1
        self.data_stats["total_cycles_extracted"] += len(all_cycles_data)

        if all_cycles_data:
            total_points = sum(len(data) for data in all_cycles_data.values())
            self.data_stats["avg_points_per_cycle"] = total_points / len(
                all_cycles_data
            )

        # 存储所有结果
        all_segments_results = []
        cycle_summary_results = []

        log(f"开始分析 {len(all_cycles_data)} 个循环...")

        for cycle_num in sorted(all_cycles_data.keys()):
            cycle_data = all_cycles_data[cycle_num]

            log(f"处理循环 {cycle_num}...")

            # 新的两阶段段划分逻辑
            high_segment, low_segment = self.extract_two_stage_segments(
                cycle_data, current_threshold=getattr(self, "manual_threshold", None)
            )

            if high_segment is None or low_segment is None:
                log(f"  循环 {cycle_num}: 无法划分为两个段，跳过")
                continue

            log(f"  循环 {cycle_num}: 成功划分为两个段")

            # 对大电流段和小电流段分别采样
            high_start, high_end = high_segment
            low_start, low_end = low_segment

            # 大电流段采样（用于记录，但不计算软指标）
            high_sample_indices = self.sample_high_current_segment(
                cycle_data, high_segment
            )

            # 小电流段采样（用于软指标计算）
            low_sample_indices = self.sample_low_current_segment(
                cycle_data, low_segment
            )

            if len(low_sample_indices) < 5:
                log(
                    f"  循环 {cycle_num}: 小电流段采样点不足({len(low_sample_indices)}个)，跳过"
                )
                continue

            # 只对小电流段计算软指标
            segment_idx = 0  # 现在只有一个小电流段
            start_idx, end_idx = low_start, low_end

            # 提取小电流段数据并应用采样
            segment_time = cycle_data["Time(s)"].iloc[start_idx:end_idx].values
            segment_current = cycle_data["Current(A)"].iloc[start_idx:end_idx].values

            # 使用小电流段的采样索引（已经检查过数量）
            valid_indices = low_sample_indices

            # 使用采样后的数据
            segment_data = {
                "t": segment_time[valid_indices],
                "I": segment_current[valid_indices],
                "V": cycle_data.get("Voltage(V)", pd.Series([0] * len(cycle_data)))
                .iloc[start_idx:end_idx]
                .values[valid_indices],
                "Q": cycle_data.get("Capacity(Ah)", pd.Series([0] * len(cycle_data)))
                .iloc[start_idx:end_idx]
                .values[valid_indices],
            }

            log(
                f"    小电流段软指标计算: 原始点数={end_idx-start_idx}, 采样点数={len(valid_indices)}"
            )

            # 计算7个软指标（只针对小电流段）
            soft_metrics = self.compute_seven_soft_metrics(segment_data)

            # 获取对应的容量和CE数据
            cycle_capacity = cycle_capacities.get(cycle_num, None)
            cycle_ce = cycle_ce_values.get(cycle_num, None)

            # 计算滑动窗口容量衰减率
            fade_rate = self.calculate_sliding_window_fade_rate(
                cycle_num, cycle_capacities
            )

            # 段级别统计信息
            segment_stats = {
                "duration": segment_data["t"][-1] - segment_data["t"][0],
                "avg_current": np.mean(np.abs(segment_data["I"])),
                "min_current": np.min(np.abs(segment_data["I"])),
                "max_current": np.max(np.abs(segment_data["I"])),
                "data_points": len(segment_data["t"]),
            }

            # 保存段级别结果
            segment_result = {
                "cycle_num": cycle_num,
                "segment_idx": segment_idx,
                "start_time": segment_data["t"][0],
                "end_time": segment_data["t"][-1],
                "duration_s": segment_stats["duration"],
                "data_points": segment_stats["data_points"],
                "avg_current_A": segment_stats["avg_current"],
                "min_current_A": segment_stats["min_current"],
                "max_current_A": segment_stats["max_current"],
                # 7个软指标
                "S_plat": soft_metrics["S_plat"],
                "S_bump": soft_metrics["S_bump"],
                "S_mono": soft_metrics["S_mono"],
                "S_amp": soft_metrics["S_amp"],
                "Q_tilde": soft_metrics["Q_tilde"],
                "C_low": soft_metrics["C_low"],
                "C_kappa": soft_metrics["C_kappa"],
                # 关联数据
                "discharge_capacity_Ah": cycle_capacity,
                "coulombic_efficiency": cycle_ce,
                "capacity_fade_rate_Ah_per_cycle": fade_rate,
            }

            all_segments_results.append(segment_result)

            # 循环级别摘要
            cycle_summary = {
                "cycle_num": cycle_num,
                "num_low_current_segments": 1,  # 现在固定为1个小电流段
                "total_low_current_duration": segment_stats[
                    "duration"
                ],  # 小电流段的持续时间
                "discharge_capacity_Ah": cycle_capacity,
                "coulombic_efficiency": cycle_ce,
                "capacity_fade_rate_Ah_per_cycle": fade_rate,
                "cycle_data_points": len(cycle_data),
                "cycle_duration_s": cycle_data["Time(s)"].iloc[-1]
                - cycle_data["Time(s)"].iloc[0],
            }

            cycle_summary_results.append(cycle_summary)

        # 输出CSV文件
        output_files = {}

        # 1. 段级别详细结果
        if all_segments_results:
            segments_df = pd.DataFrame(all_segments_results)
            segments_csv = output_path / "lithium_detection_segments_analysis.csv"
            segments_df.to_csv(segments_csv, index=False)
            output_files["segments"] = str(segments_csv)
            log(f"段级别分析结果已保存到: {segments_csv}")
            log(f"总共分析了 {len(all_segments_results)} 个低电流段")

        # 2. 循环级别摘要
        if cycle_summary_results:
            summary_df = pd.DataFrame(cycle_summary_results)
            summary_csv = output_path / "lithium_detection_cycles_summary.csv"
            summary_df.to_csv(summary_csv, index=False)
            output_files["summary"] = str(summary_csv)
            log(f"循环级别摘要已保存到: {summary_csv}")

        # 3. 生成统计报告
        stats_report = self._generate_analysis_stats_report(
            all_segments_results, cycle_summary_results
        )
        stats_csv = output_path / "lithium_detection_statistics.csv"
        pd.DataFrame([stats_report]).to_csv(stats_csv, index=False)
        output_files["statistics"] = str(stats_csv)
        log(f"统计报告已保存到: {stats_csv}")

        log("所有分析完成！")
        return output_files

    def _generate_analysis_stats_report(
        self, segments_results: List[Dict], cycles_results: List[Dict]
    ) -> Dict:
        """生成分析统计报告"""
        if not segments_results:
            return {"error": "No segments analyzed"}

        segments_df = pd.DataFrame(segments_results)
        cycles_df = pd.DataFrame(cycles_results)

        # 软指标统计
        soft_metrics_cols = [
            "S_plat",
            "S_bump",
            "S_mono",
            "S_amp",
            "Q_tilde",
            "C_low",
            "C_kappa",
        ]

        stats_report = {
            "total_cycles_analyzed": len(cycles_df),
            "total_segments_analyzed": len(segments_df),
            "avg_segments_per_cycle": (
                len(segments_df) / len(cycles_df) if len(cycles_df) > 0 else 0
            ),
            "avg_segment_duration_s": segments_df["duration_s"].mean(),
            "avg_current_in_segments_A": segments_df["avg_current_A"].mean(),
            "capacity_fade_rate_avg": (
                cycles_df["capacity_fade_rate_Ah_per_cycle"].mean()
                if "capacity_fade_rate_Ah_per_cycle" in cycles_df.columns
                else 0
            ),
            "capacity_fade_rate_std": (
                cycles_df["capacity_fade_rate_Ah_per_cycle"].std()
                if "capacity_fade_rate_Ah_per_cycle" in cycles_df.columns
                else 0
            ),
            "avg_coulombic_efficiency": (
                cycles_df["coulombic_efficiency"].mean()
                if "coulombic_efficiency" in cycles_df.columns
                else 0
            ),
        }

        # 添加软指标统计
        for metric in soft_metrics_cols:
            if metric in segments_df.columns:
                stats_report[f"{metric}_mean"] = segments_df[metric].mean()
                stats_report[f"{metric}_std"] = segments_df[metric].std()
                stats_report[f"{metric}_max"] = segments_df[metric].max()
                stats_report[f"{metric}_min"] = segments_df[metric].min()

        return stats_report


# ======================
# 主要分析功能
# ======================


def classify_lithium_plating_risk(fade_rate_percent_per_cycle):
    """基于容量衰减速率划分析锂风险等级"""
    if fade_rate_percent_per_cycle <= 0.02:  # ≤0.02%/cycle
        return "低风险"
    elif fade_rate_percent_per_cycle <= 0.05:  # 0.02-0.05%/cycle
        return "中风险"
    else:  # >0.05%/cycle
        return "高风险"


def classify_degradation_stage(capacity_retention_percent):
    """基于容量保持率划分电池衰减阶段"""
    if capacity_retention_percent >= 90:
        return "初期"  # ≥90%
    elif capacity_retention_percent >= 80:
        return "中期"  # 80-90%
    else:
        return "后期"  # <80%


def get_json_files(input_path):
    """获取需要处理的JSON文件列表"""
    input_path = Path(input_path)

    if input_path.is_file():
        # 单个文件
        if input_path.suffix.lower() == ".json":
            return [input_path]
        else:
            raise ValueError(f"输入文件不是JSON格式: {input_path}")
    elif input_path.is_dir():
        # 文件夹
        json_files = list(input_path.glob("*.json"))
        if not json_files:
            raise ValueError(f"文件夹中没有找到JSON文件: {input_path}")
        return sorted(json_files)  # 按名称排序
    else:
        raise ValueError(f"输入路径不存在: {input_path}")


def generate_direct_comprehensive_analysis(
    json_file_path, analyzer, output_dir, enable_diagnosis=True
):
    """直接从JSON文件生成综合分析CSV文件和析锂诊断报告"""
    json_file_path = Path(json_file_path)
    file_stem = json_file_path.stem

    print(f"\n{'='*60}")
    print(f"正在处理文件: {json_file_path.name}")
    print(f"{'='*60}")

    try:
        # 运行完整分析
        output_files = analyzer.analyze_all_cycles_for_lithium_detection(
            str(json_file_path), output_dir
        )

        # 读取分析结果
        if "segments" not in output_files:
            log("⚠️  警告: 未找到任何有效的低电流段，无法进行进一步分析")
            log("可能原因:")
            log("1. 电流阈值设置过严格")
            log("2. 数据质量问题")
            log("3. CV阶段特征不明显")
            return

        segments_file = output_files["segments"]
        summary_file = output_files["summary"]

        segments_df = pd.read_csv(segments_file)
        summary_df = pd.read_csv(summary_file)

        # 为了避免列名冲突，先重命名summary_df中的重复列
        # 只保留summary_df中unique的列
        summary_cols_to_keep = [
            "cycle_num",
            "num_low_current_segments",
            "total_low_current_duration",
            "cycle_data_points",
            "cycle_duration_s",
        ]
        summary_df_clean = summary_df[summary_cols_to_keep]

        # 合并数据（segments_df包含所有需要的指标）
        merged_df = segments_df.merge(summary_df_clean, on="cycle_num", how="inner")

        # 计算初始容量
        initial_capacity = merged_df.iloc[0]["discharge_capacity_Ah"]

        # 创建综合分析数据
        comprehensive_data = []
        cumulative_time_s = 0

        for idx, row in merged_df.iterrows():
            cycle_num = row["cycle_num"]

            # 基本信息
            cycle_duration_s = row["cycle_duration_s"]
            test_time_hours = cumulative_time_s / 3600
            cycle_start_time_s = cumulative_time_s

            # 电池健康状态指标
            discharge_capacity = row["discharge_capacity_Ah"]
            capacity_retention_percent = (discharge_capacity / initial_capacity) * 100
            capacity_fade_rate_Ah = row["capacity_fade_rate_Ah_per_cycle"]
            capacity_fade_rate_percent = (
                capacity_fade_rate_Ah / initial_capacity
            ) * 100
            coulombic_efficiency = row["coulombic_efficiency"]
            coulombic_efficiency_deviation = abs(coulombic_efficiency - 1.0)

            # 低电流段特征
            cv_segment_duration_s = row["duration_s"]
            cv_avg_current_A = row["avg_current_A"]
            cv_min_current_A = row["min_current_A"]
            cv_max_current_A = row["max_current_A"]
            cv_duration_ratio = cv_segment_duration_s / cycle_duration_s

            # 7个关键软指标
            S_plat = row["S_plat"]
            S_bump = row["S_bump"]
            S_mono = row["S_mono"]
            S_amp = row["S_amp"]
            Q_tilde = row["Q_tilde"]
            C_low = row["C_low"]
            C_kappa = row["C_kappa"]

            # 风险等级和衰减阶段
            lithium_plating_risk = classify_lithium_plating_risk(
                abs(capacity_fade_rate_percent)
            )
            degradation_stage = classify_degradation_stage(capacity_retention_percent)

            # 构建综合数据行
            comprehensive_row = {
                # 基本信息
                "cycle_num": cycle_num,
                "test_time_hours": test_time_hours,
                "cycle_duration_s": cycle_duration_s,
                "cycle_start_time_s": cycle_start_time_s,
                # 电池健康状态指标
                "discharge_capacity_Ah": discharge_capacity,
                "capacity_retention_percent": capacity_retention_percent,
                "capacity_fade_rate_Ah_per_cycle": capacity_fade_rate_Ah,
                "capacity_fade_rate_percent_per_cycle": capacity_fade_rate_percent,
                "coulombic_efficiency": coulombic_efficiency,
                "coulombic_efficiency_deviation": coulombic_efficiency_deviation,
                # 低电流段特征
                "cv_segment_duration_s": cv_segment_duration_s,
                "cv_avg_current_A": cv_avg_current_A,
                "cv_min_current_A": cv_min_current_A,
                "cv_max_current_A": cv_max_current_A,
                "cv_duration_ratio": cv_duration_ratio,
                # 7个关键软指标
                "S_plat": S_plat,
                "S_bump": S_bump,
                "S_mono": S_mono,
                "S_amp": S_amp,
                "Q_tilde": Q_tilde,
                "C_low": C_low,
                "C_kappa": C_kappa,
                # 分类结果
                "lithium_plating_risk_level": lithium_plating_risk,
                "battery_degradation_stage": degradation_stage,
            }

            comprehensive_data.append(comprehensive_row)
            cumulative_time_s += cycle_duration_s

        # 创建数据框并保存
        comprehensive_df = pd.DataFrame(comprehensive_data)
        output_filename = f"{file_stem}_analysis.csv"
        output_file = Path(output_dir) / output_filename
        comprehensive_df.to_csv(output_file, index=False, float_format="%.6f")

        # 析锂诊断分析
        diagnosis_result = None
        diagnosis_file = None

        if enable_diagnosis and DIAGNOSTIC_AVAILABLE:
            print(f"\n{'='*40}")
            print(f"正在进行析锂诊断分析...")
            print(f"{'='*40}")

            try:
                # 初始化诊断模型
                diagnostic = LithiumPlatingDiagnostic(baseline_cycles=(20, 30))

                # 进行整体诊断
                diagnosis_result = diagnostic.diagnose_battery(comprehensive_df)

                # 保存诊断报告
                diagnosis_filename = f"{file_stem}_lithium_diagnosis.json"
                diagnosis_file = Path(output_dir) / diagnosis_filename
                save_diagnosis_report(diagnosis_result, str(diagnosis_file))

                # 添加诊断结果到综合分析CSV中
                # 为每个循环添加诊断信息
                cycle_diagnoses = {
                    d["cycle_num"]: d for d in diagnosis_result["cycle_diagnoses"]
                }

                # 添加诊断列
                comprehensive_df["diagnosis_stage"] = comprehensive_df["cycle_num"].map(
                    lambda x: cycle_diagnoses.get(x, {}).get("stage", "unknown")
                )
                comprehensive_df["diagnosis_risk_score"] = comprehensive_df[
                    "cycle_num"
                ].map(lambda x: cycle_diagnoses.get(x, {}).get("risk_score", 0.0))
                comprehensive_df["diagnosis_confidence"] = comprehensive_df[
                    "cycle_num"
                ].map(lambda x: cycle_diagnoses.get(x, {}).get("confidence", 0.0))

                # 重新保存带诊断信息的CSV
                comprehensive_df.to_csv(output_file, index=False, float_format="%.6f")

                print(f"\n🔍 析锂诊断结果:")
                print(f"  整体状态: {diagnosis_result['overall_status']}")
                print(
                    f"  平均风险评分: {diagnosis_result['summary_statistics']['average_risk_score']:.3f}"
                )
                print(
                    f"  最大风险评分: {diagnosis_result['summary_statistics']['max_risk_score']:.3f}"
                )

                if diagnosis_result["summary_statistics"]["first_warning_cycle"]:
                    print(
                        f"  首次预警循环: {diagnosis_result['summary_statistics']['first_warning_cycle']}"
                    )

                # 状态分布
                stats = diagnosis_result["summary_statistics"]
                total_cycles = stats["total_cycles"]
                print(f"\n📊 诊断状态分布:")
                if stats["normal_cycles"] > 0:
                    print(
                        f"  正常: {stats['normal_cycles']} 循环 ({stats['normal_cycles']/total_cycles*100:.1f}%)"
                    )
                if stats["early_warning_cycles"] > 0:
                    print(
                        f"  早期预警: {stats['early_warning_cycles']} 循环 ({stats['early_warning_cycles']/total_cycles*100:.1f}%)"
                    )
                if stats["confirmed_plating_cycles"] > 0:
                    print(
                        f"  析锂确认: {stats['confirmed_plating_cycles']} 循环 ({stats['confirmed_plating_cycles']/total_cycles*100:.1f}%)"
                    )
                if stats["severe_plating_cycles"] > 0:
                    print(
                        f"  大量析锂: {stats['severe_plating_cycles']} 循环 ({stats['severe_plating_cycles']/total_cycles*100:.1f}%)"
                    )

                # 预测信息
                if diagnosis_result["prediction"]:
                    pred = diagnosis_result["prediction"]
                    print(f"\n🔮 趋势预测:")
                    print(f"  风险趋势: {pred['risk_trend']}")
                    if pred.get("estimated_cycles_to_warning"):
                        print(
                            f"  预计预警时间: {pred['estimated_cycles_to_warning']} 循环"
                        )
                    if pred.get("estimated_cycles_to_failure"):
                        print(
                            f"  预计衰减加速: {pred['estimated_cycles_to_failure']} 循环"
                        )

                # 维护建议
                if diagnosis_result["recommendations"]:
                    print(f"\n💡 维护建议:")
                    for i, rec in enumerate(diagnosis_result["recommendations"], 1):
                        print(f"  {i}. {rec}")

            except Exception as e:
                print(f"⚠️ 析锂诊断过程中出现错误: {str(e)}")
                diagnosis_result = None
                diagnosis_file = None

        # 删除临时文件
        for temp_file in output_files.values():
            try:
                os.remove(temp_file)
            except:
                pass

        print(f"\n📁 生成的文件:")
        print("-" * 40)
        print(f"analysis: {output_filename}")
        if diagnosis_file:
            print(f"diagnosis: {diagnosis_file.name}")

        # 打印统计信息
        print(f"\n📊 基础统计信息:")
        print(f"总循环数: {len(comprehensive_df)}")
        print(f"初始容量: {initial_capacity:.3f} Ah")
        print(f"最终容量: {comprehensive_df.iloc[-1]['discharge_capacity_Ah']:.3f} Ah")
        print(
            f"容量保持率: {comprehensive_df.iloc[-1]['capacity_retention_percent']:.1f}%"
        )
        print(f"累积测试时间: {comprehensive_df.iloc[-1]['test_time_hours']:.1f} 小时")

        # 风险等级分布
        risk_counts = comprehensive_df["lithium_plating_risk_level"].value_counts()
        print(f"\n⚠️ 析锂风险等级分布:")
        for risk_level, count in risk_counts.items():
            print(
                f"  {risk_level}: {count} 个循环 ({count/len(comprehensive_df)*100:.1f}%)"
            )

        # 衰减阶段分布
        stage_counts = comprehensive_df["battery_degradation_stage"].value_counts()
        print(f"\n🔋 电池衰减阶段分布:")
        for stage, count in stage_counts.items():
            print(f"  {stage}: {count} 个循环 ({count/len(comprehensive_df)*100:.1f}%)")

        result_files = {"analysis": str(output_file)}
        if diagnosis_file:
            result_files["diagnosis"] = str(diagnosis_file)

        return result_files

    except Exception as e:
        print(f"错误: 处理文件 {json_file_path.name} 时出现异常")
        print(f"详细信息: {str(e)}")
        return None


def process_single_file(json_file_path, analyzer, output_dir, enable_diagnosis=True):
    """处理单个JSON文件"""
    # 生成综合分析文件和析锂诊断报告
    result = generate_direct_comprehensive_analysis(
        json_file_path, analyzer, output_dir, enable_diagnosis
    )
    return result


def main():
    """主函数：命令行界面"""
    parser = argparse.ArgumentParser(
        description="Soft Metrics Analyzer - 电池析锂检测软指标分析器（自包含版本）"
    )
    parser.add_argument(
        "input_path", type=str, help="输入的JSON文件路径或包含JSON文件的文件夹路径"
    )
    parser.add_argument(
        "--output", "-o", type=str, default="./output", help="输出目录 (默认: ./output)"
    )
    parser.add_argument(
        "--current-threshold",
        "-c",
        type=float,
        default=None,
        help="电流阈值 (默认: None，自动计算为Icv_max/3；可手动指定具体数值如2.0)",
    )
    parser.add_argument(
        "--window-size",
        "-w",
        type=int,
        default=10,
        help="容量衰减率计算的滑动窗口大小 (默认: 10个循环)",
    )
    parser.add_argument(
        "--no-diagnosis",
        action="store_true",
        help="禁用析锂诊断功能，仅生成基础分析报告",
    )

    args = parser.parse_args()

    # 获取需要处理的JSON文件列表
    try:
        json_files = get_json_files(args.input_path)
    except ValueError as e:
        print(f"错误: {str(e)}")
        sys.exit(1)

    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)

    # 配置数据加载器 - 基于CV阶段析锂检测的物理参数
    config = {
        "sampling_rate": 1.0,
        "physics_params": {
            # 平台检测参数 - 针对CV阶段优化
            "tau_slope": 0.001,  # 降低阈值，提高CV阶段平台检测敏感度
            "sigma_p": 0.0005,  # 提高平台检测精度
            # 凸起检测参数 - 适应CV阶段电流特征
            "kappa_pos": 0.0003,  # 适应CV阶段较小的曲率变化
            "kappa_neg": -0.0003,  # 匹配CV阶段的物理特性
            "sigma_b": 0.0008,  # 提高凸起检测灵敏度
            # 基线参数 - 适应CV指数衰减特性
            "alpha_baseline": 0.95,  # 适应CV阶段更快的衰减
            # 低电流阈值 - 基于CV后期特征
            "i_threshold": 0.3,  # 提高到CV终止电流水平 (6A/20)
        },
        "downsample_params": {"i_threshold": 0.5, "factor_high": 2, "factor_low": 1},
    }

    # 初始化分析器
    analyzer = LithiumDetectionDataLoader(
        config, manual_threshold=args.current_threshold
    )

    # 确定是否启用诊断功能
    enable_diagnosis = not args.no_diagnosis and DIAGNOSTIC_AVAILABLE

    print("=" * 60)
    print("Soft Metrics Analyzer - 电池析锂检测软指标分析器（自包含版本）")
    print("=" * 60)
    print(f"输入路径: {args.input_path}")
    print(f"找到 {len(json_files)} 个JSON文件")
    print(f"输出目录: {args.output}")
    if args.current_threshold is not None:
        print(f"电流阈值: 手动指定 ({args.current_threshold}A)")
    else:
        print(f"电流阈值: 自动计算 (Icv_max/3)")
    print(f"滑动窗口: {args.window_size}个循环")
    if enable_diagnosis:
        print("输出格式: 27列综合分析文件 (24列基础 + 3列诊断)")
    else:
        print("输出格式: 24列基础分析文件")
    if enable_diagnosis:
        print("🔍 析锂诊断: 启用 (包含智能预警和风险评估)")
    else:
        print("❌ 析锂诊断: 禁用 (仅基础分析)")
    print("=" * 60)

    # 处理所有文件
    successful_files = []
    failed_files = []

    for i, json_file in enumerate(json_files, 1):
        print(f"\n[{i}/{len(json_files)}] 处理文件: {json_file.name}")

        result = process_single_file(
            json_file, analyzer, str(output_dir), enable_diagnosis
        )

        if result:
            successful_files.append((json_file.name, result))
        else:
            failed_files.append(json_file.name)

    # 打印总结
    print("\n" + "=" * 60)
    print("处理完成！总结:")
    print("=" * 60)
    print(f"成功处理: {len(successful_files)} 个文件")
    print(f"失败文件: {len(failed_files)} 个文件")

    if successful_files:
        print(f"\n成功处理的文件:")
        for filename, files in successful_files:
            print(f"  {filename}:")
            for file_type, file_path in files.items():
                print(f"    {file_type:12}: {Path(file_path).name}")

    if failed_files:
        print(f"\n失败的文件:")
        for filename in failed_files:
            print(f"  {filename}")

    print("\n📄 输出文件说明:")
    if enable_diagnosis:
        print("- analysis : 综合分析文件（27列格式：24列基础+3列诊断结果）")
        print("- diagnosis: 析锂诊断报告（JSON格式，包含详细预警信息）")
    else:
        print("- analysis : 综合分析文件（24列统一格式：7个软指标+健康状态+风险评估）")
    print("- 详细说明请参考: soft_metrics_analyzer_guide.md")

    print("\n🎯 建议后续步骤:")
    print("1. 打开 {filename}_analysis.csv 进行综合分析")
    print("2. 关注 S_plat, S_bump, S_amp 等关键软指标趋势")
    if enable_diagnosis:
        print("3. 查看析锂诊断报告了解智能预警结果")
        print("4. 监控 diagnosis_stage 和 diagnosis_risk_score 列")
        print("5. 根据维护建议调整充电策略")
        print("6. 分析容量衰减率与软指标的关联性")
        print("7. 对比不同电池的综合分析结果")
        print("8. 验证自适应阈值效果 (查看日志中的阈值选择信息)")
        print("9. 分析采样统计，了解物理采样条件的效果")
    else:
        print("3. 分析容量衰减率与软指标的关联性")
        print("4. 监控析锂风险等级和电池衰减阶段变化")
        print("5. 对比不同电池的综合分析结果")
        print("6. 验证自适应阈值效果 (查看日志中的阈值选择信息)")
        print("7. 分析采样统计，了解物理采样条件的效果")

    if failed_files:
        print("\n注意: 部分文件处理失败，请检查文件格式和内容")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有命令行参数，显示使用说明
        print("=" * 60)
        print("Soft Metrics Analyzer - 电池析锂检测软指标分析器（自包含版本）")
        print("=" * 60)
        print("使用方法:")
        print("  python soft_metrics_analyzer.py <input_path> [options]")
        print("")
        print("参数:")
        print("  input_path                  JSON文件或文件夹路径")
        print("  --output, -o               输出目录 (默认: ./output)")
        print("  --current-threshold, -c    电流阈值 (默认: 自动计算为Icv_max/3)")
        print("  --window-size, -w          滑动窗口大小 (默认: 10个循环)")
        print("  --no-diagnosis             禁用析锂诊断功能")
        print("")
        print("阈值设置:")
        print("  默认: 自动计算为 Icv_max/3 (基于CV阶段物理特性)")
        print("  手动: 可指定具体数值，如 -c 2.0 表示2.0A阈值")
        print("")
        print("示例:")
        print("  python soft_metrics_analyzer.py data.json")
        print("  python soft_metrics_analyzer.py data.json -c 2.0")
        print("  python soft_metrics_analyzer.py folder/ -o results")
        print("  python soft_metrics_analyzer.py data.json --no-diagnosis")
    else:
        # 运行命令行界面
        main()


"""
=== Soft Metrics Analyzer (Standalone Version) ===
电池析锂检测软指标分析器（自包含版本）

🔬 核心技术特性:
- 自包含设计，无需外部data_loader_XJTU.py依赖
- 集成完整的数据处理和软指标计算功能
- 支持自适应降采样和向量化 dI/dt 计算
- 智能电流阈值计算 (Icv_max/3 物理模型)
- 基于CV阶段特性的物理采样条件
- 内置数据质量验证和物理约束检查
- 智能析锂诊断系统（可选）

📊 数据处理流程:
1. JSON数据加载 → extract_cycles_from_json_integrated()
2. 自适应预处理 → adaptive_downsample_data()
3. dI/dt增强计算 → compute_dIdt_vectorized() (时间间隔≥3s过滤)
4. 智能阈值计算 → _calculate_optimal_threshold() (阈值=Icv_max/3)
5. 物理采样条件 → _generate_sampling_mask() (基于时间+电流变化)
6. 低电流段检测 → extract_low_current_segments() (集成采样条件)
7. 软指标计算 → compute_seven_soft_metrics()
8. 智能诊断分析 → LithiumPlatingDiagnostic()（可选）

🎯 默认输出文件格式：
- {filename}_analysis.csv: 27列格式 (24列基础 + 3列诊断)
- {filename}_lithium_diagnosis.json: 详细诊断报告

禁用诊断时输出：
- {filename}_analysis.csv: 24列基础格式

📈 七个核心软指标:
- S_plat: 平台度 (>0.7高风险) → _compute_platform_degree()
- S_bump: 凸起度 (>0.6高风险) → _compute_bump_degree()
- S_mono: 非单调性 (>0.4中风险) → _compute_non_monotonicity()
- S_amp: 回嵌幅度 (>0.5高风险) → _compute_reintercalation_amplitude()
- Q_tilde: 锂量代理 (监控趋势) → _compute_lithium_proxy()
- C_low: 低电流一致性 (<0.5风险) → _compute_low_current_consistency()
- C_kappa: 电流形状因子 (<0.5风险) → _compute_curvature_consistency()

🔧 集成功能模块:
- LithiumDetectionDataLoader: 核心数据加载和分析引擎（内置）
- 软指标计算: 7个物理软指标的完整计算逻辑
- 数据预处理: 自适应降采样和向量化计算
- 风险评估: 基于物理参数的风险等级划分

📦 依赖要求:
- numpy>=1.19.0, pandas>=1.3.0, scipy>=1.7.0 (核心计算)
- 可选: lithium_plating_diagnostic.py (智能诊断功能)

📋 使用说明:
python soft_metrics_analyzer.py <input_path> [options]

🎯 典型分析结果示例（基于新物理模型）：
- 处理循环数：298个
- 智能阈值：自动计算 (如6A→2A, 3A→1A)
- 采样策略：基于时间间隔+电流变化的物理条件
- 容量衰减：1.983 → 1.605 Ah (80.9%保持率)
- 测试时长：124.0小时
- 风险分布：高风险45.3%, 中风险34.6%, 低风险20.1%
- 衰减阶段：初期84.9%, 中期15.1%
"""
