#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
析锂诊断模型
基于软指标的锂离子电池析锂早期预警与诊断系统

核心功能：
1. 建立第20-30圈的软指标基线
2. 计算软指标偏离度和趋势特征
3. 分层析锂诊断（早期预警/析锂确认/大量析锂）
4. 提供风险评分和预测时间窗口

作者：Claude AI
版本：1.0
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import warnings
from scipy import stats
from scipy.signal import savgol_filter
import json


class LithiumPlatingDiagnostic:
    """析锂诊断模型类"""

    def __init__(self, baseline_cycles: Tuple[int, int] = (20, 30)):
        """
        初始化析锂诊断模型

        Args:
            baseline_cycles: 基线建立的循环范围 (start, end)
        """
        self.baseline_start, self.baseline_end = baseline_cycles
        self.baseline_stats = {}
        self.soft_metrics_columns = [
            "S_plat",
            "S_bump",
            "S_mono",
            "S_amp",
            "Q_tilde",
            "C_low",
            "C_kappa",
        ]
        self.is_fitted = False

        # 预定义权重（基于物理机理）
        self.feature_weights = {
            "S_plat": 0.25,  # 析锂平台特征 - 核心指标
            "S_amp": 0.25,  # 析锂幅度特征 - 核心指标
            "S_bump": 0.15,  # 凸起特征
            "S_mono": 0.15,  # 单调性特征
            "Q_tilde": 0.1,  # 容量相关特征
            "C_low": 0.05,  # 低电流特征
            "C_kappa": 0.05,  # 一致性特征
        }

        # 诊断阈值（可调整）
        self.thresholds = {
            "early_warning": {
                "deviation_threshold": 2.0,  # 偏离基线2倍标准差
                "trend_threshold": 0.05,  # 趋势变化阈值
                "multi_indicator_count": 2,  # 至少2个指标异常
            },
            "confirmed_plating": {
                "deviation_threshold": 3.0,  # 偏离基线3倍标准差
                "trend_threshold": 0.1,  # 强趋势变化
                "capacity_fade_acceleration": 1.5,  # 容量衰减加速因子
                "multi_indicator_count": 3,  # 至少3个指标异常
            },
            "severe_plating": {
                "capacity_fade_rate": 0.05,  # 容量衰减速率 >5%/cycle
                "ce_deviation": 0.02,  # 库伦效率偏差 >2%
                "multi_indicator_count": 4,  # 至少4个指标异常
            },
        }

    def establish_baseline(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        建立软指标基线（第20-30圈）

        Args:
            data: 包含软指标的DataFrame

        Returns:
            baseline_stats: 基线统计信息
        """
        # 过滤基线循环范围
        baseline_data = data[
            (data["cycle_num"] >= self.baseline_start)
            & (data["cycle_num"] <= self.baseline_end)
        ]

        if len(baseline_data) < 5:
            warnings.warn(f"基线数据点过少 ({len(baseline_data)}点)，建议至少5个循环")

        baseline_stats = {}

        for metric in self.soft_metrics_columns:
            if metric in baseline_data.columns:
                values = baseline_data[metric].dropna()
                if len(values) > 0:
                    baseline_stats[metric] = {
                        "mean": np.mean(values),
                        "std": np.std(values),
                        "median": np.median(values),
                        "q25": np.percentile(values, 25),
                        "q75": np.percentile(values, 75),
                        "min": np.min(values),
                        "max": np.max(values),
                        "count": len(values),
                    }

        # 建立容量基线（用于性能评估）
        if "discharge_capacity_Ah" in baseline_data.columns:
            cap_values = baseline_data["discharge_capacity_Ah"].dropna()
            if len(cap_values) > 2:
                # 拟合线性趋势
                cycles = baseline_data[baseline_data["discharge_capacity_Ah"].notna()][
                    "cycle_num"
                ].values
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    cycles, cap_values
                )

                baseline_stats["capacity"] = {
                    "mean": np.mean(cap_values),
                    "std": np.std(cap_values),
                    "linear_slope": slope,
                    "r_squared": r_value**2,
                    "baseline_fade_rate": abs(slope),
                }

        self.baseline_stats = baseline_stats
        self.is_fitted = True

        return baseline_stats

    def calculate_deviations(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算软指标相对于基线的偏离度

        Args:
            data: 包含软指标的DataFrame

        Returns:
            data_with_deviations: 添加了偏离度特征的DataFrame
        """
        if not self.is_fitted:
            raise ValueError("模型未拟合，请先调用 establish_baseline()")

        data_copy = data.copy()

        # 计算标准化偏离度
        for metric in self.soft_metrics_columns:
            if metric in data.columns and metric in self.baseline_stats:
                baseline_mean = self.baseline_stats[metric]["mean"]
                baseline_std = self.baseline_stats[metric]["std"]

                if baseline_std > 0:
                    # Z-score 偏离度
                    data_copy[f"{metric}_deviation"] = (
                        data[metric] - baseline_mean
                    ) / baseline_std

                    # 相对偏离度 (%)
                    data_copy[f"{metric}_relative_deviation"] = (
                        (data[metric] - baseline_mean) / baseline_mean * 100
                    )

                    # 超出基线范围的程度
                    upper_bound = baseline_mean + 2 * baseline_std
                    lower_bound = baseline_mean - 2 * baseline_std
                    data_copy[f"{metric}_beyond_bounds"] = np.where(
                        data[metric] > upper_bound,
                        (data[metric] - upper_bound) / baseline_std,
                        np.where(
                            data[metric] < lower_bound,
                            (lower_bound - data[metric]) / baseline_std,
                            0,
                        ),
                    )

        return data_copy

    def calculate_trend_features(
        self, data: pd.DataFrame, window: int = 10
    ) -> pd.DataFrame:
        """
        计算软指标的趋势特征

        Args:
            data: 包含软指标的DataFrame
            window: 移动窗口大小

        Returns:
            data_with_trends: 添加了趋势特征的DataFrame
        """
        data_copy = data.copy()

        for metric in self.soft_metrics_columns:
            if metric in data.columns:
                values = data[metric].values

                # 移动平均
                data_copy[f"{metric}_ma"] = (
                    data[metric].rolling(window=window, min_periods=3).mean()
                )

                # 移动标准差（稳定性指标）
                data_copy[f"{metric}_moving_std"] = (
                    data[metric].rolling(window=window, min_periods=3).std()
                )

                # 趋势斜率（线性回归斜率）
                def calculate_slope(series):
                    if len(series.dropna()) < 3:
                        return np.nan
                    x = np.arange(len(series))
                    y = series.values
                    valid_idx = ~np.isnan(y)
                    if np.sum(valid_idx) < 3:
                        return np.nan
                    slope, _, _, _, _ = stats.linregress(x[valid_idx], y[valid_idx])
                    return slope

                data_copy[f"{metric}_trend_slope"] = (
                    data[metric]
                    .rolling(window=window, min_periods=3)
                    .apply(calculate_slope)
                )

                # 加速度（二阶导数近似）
                if len(values) > 2:
                    # 使用Savitzky-Golay滤波器平滑并计算导数
                    if len(values) >= 5:
                        try:
                            smoothed = savgol_filter(
                                values,
                                window_length=min(5, len(values) // 2 * 2 + 1),
                                polyorder=2,
                            )
                            acceleration = np.gradient(np.gradient(smoothed))
                            data_copy[f"{metric}_acceleration"] = acceleration
                        except:
                            data_copy[f"{metric}_acceleration"] = np.nan
                    else:
                        data_copy[f"{metric}_acceleration"] = np.nan

        return data_copy

    def calculate_interaction_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算交互特征（组合指标）

        Args:
            data: 包含软指标的DataFrame

        Returns:
            data_with_interactions: 添加了交互特征的DataFrame
        """
        data_copy = data.copy()

        # 核心析锂特征组合
        if all(col in data.columns for col in ["S_plat", "S_amp"]):
            data_copy["plating_signature"] = data["S_plat"] * data["S_amp"]

        # 形态异常特征组合
        if all(col in data.columns for col in ["S_bump", "S_mono"]):
            data_copy["morphology_index"] = data["S_bump"] * data["S_mono"]

        # 稳定性指标组合
        if all(col in data.columns for col in ["C_low", "C_kappa"]):
            data_copy["stability_index"] = data["C_low"] * data["C_kappa"]

        # 综合异常评分（加权组合）
        deviation_cols = [
            col for col in data_copy.columns if col.endswith("_deviation")
        ]
        if deviation_cols:
            weighted_deviation = 0
            total_weight = 0

            for col in deviation_cols:
                metric_name = col.replace("_deviation", "")
                if metric_name in self.feature_weights:
                    weight = self.feature_weights[metric_name]
                    weighted_deviation += data_copy[col].fillna(0) * weight
                    total_weight += weight

            if total_weight > 0:
                data_copy["weighted_anomaly_score"] = weighted_deviation / total_weight

        return data_copy

    def diagnose_single_cycle(self, cycle_data: pd.Series) -> Dict[str, Any]:
        """
        单个循环的析锂诊断

        Args:
            cycle_data: 单个循环的数据（包含所有特征）

        Returns:
            diagnosis_result: 诊断结果
        """
        result = {
            "cycle_num": cycle_data.get("cycle_num", -1),
            "stage": "normal",
            "risk_score": 0.0,
            "confidence": 0.0,
            "anomalous_indicators": [],
            "key_features": {},
            "warnings": [],
        }

        # 检查是否有足够的数据
        deviation_indicators = 0
        anomaly_scores = []

        # 1. 检查软指标偏离度
        for metric in self.soft_metrics_columns:
            deviation_col = f"{metric}_deviation"
            if deviation_col in cycle_data and not pd.isna(cycle_data[deviation_col]):
                deviation = abs(cycle_data[deviation_col])

                # 记录关键特征
                result["key_features"][metric] = {
                    "value": cycle_data.get(metric, np.nan),
                    "deviation": cycle_data[deviation_col],
                    "is_anomalous": False,
                }

                # 早期预警检查
                if deviation > self.thresholds["early_warning"]["deviation_threshold"]:
                    deviation_indicators += 1
                    result["anomalous_indicators"].append(metric)
                    result["key_features"][metric]["is_anomalous"] = True
                    anomaly_scores.append(deviation)

        # 2. 检查综合异常评分
        if "weighted_anomaly_score" in cycle_data and not pd.isna(
            cycle_data["weighted_anomaly_score"]
        ):
            weighted_score = abs(cycle_data["weighted_anomaly_score"])
            anomaly_scores.append(weighted_score)

        # 3. 分层诊断
        # Level 1: 早期预警
        if (
            deviation_indicators
            >= self.thresholds["early_warning"]["multi_indicator_count"]
        ):
            result["stage"] = "early_warning"
            result["risk_score"] = min(0.3 + deviation_indicators * 0.1, 0.6)
            result["confidence"] = 0.7

        # Level 2: 析锂确认
        severe_deviations = sum(
            1
            for score in anomaly_scores
            if score > self.thresholds["confirmed_plating"]["deviation_threshold"]
        )
        if (
            severe_deviations
            >= self.thresholds["confirmed_plating"]["multi_indicator_count"]
        ):
            result["stage"] = "confirmed_plating"
            result["risk_score"] = min(0.6 + severe_deviations * 0.1, 0.9)
            result["confidence"] = 0.8

            # 检查容量衰减加速
            if "capacity_fade_rate_percent_per_cycle" in cycle_data:
                fade_rate = cycle_data["capacity_fade_rate_percent_per_cycle"]
                if fade_rate > 0.03:  # >3%/cycle
                    result["warnings"].append("检测到容量衰减加速")

        # Level 3: 大量析锂
        if (
            "capacity_fade_rate_percent_per_cycle" in cycle_data
            and cycle_data["capacity_fade_rate_percent_per_cycle"]
            > self.thresholds["severe_plating"]["capacity_fade_rate"]
        ):
            result["stage"] = "severe_plating"
            result["risk_score"] = min(0.9, max(result["risk_score"], 0.85))
            result["confidence"] = 0.9
            result["warnings"].append("检测到大量析锂")

        return result

    def diagnose_battery(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        整个电池的析锂诊断分析

        Args:
            data: 完整的电池数据

        Returns:
            battery_diagnosis: 完整的诊断结果
        """
        if not self.is_fitted:
            # 自动建立基线
            self.establish_baseline(data)

        # 计算所有特征
        data_with_features = self.calculate_deviations(data)
        data_with_features = self.calculate_trend_features(data_with_features)
        data_with_features = self.calculate_interaction_features(data_with_features)

        # 逐循环诊断
        cycle_diagnoses = []
        for idx, row in data_with_features.iterrows():
            diagnosis = self.diagnose_single_cycle(row)
            cycle_diagnoses.append(diagnosis)

        # 整体分析
        stage_counts = {}
        risk_scores = []
        warning_cycles = []

        for diag in cycle_diagnoses:
            stage = diag["stage"]
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
            risk_scores.append(diag["risk_score"])

            if diag["stage"] != "normal":
                warning_cycles.append(diag["cycle_num"])

        # 预测分析
        prediction = self._predict_future_degradation(
            data_with_features, cycle_diagnoses
        )

        battery_diagnosis = {
            "overall_status": self._determine_overall_status(stage_counts, risk_scores),
            "cycle_diagnoses": cycle_diagnoses,
            "summary_statistics": {
                "total_cycles": len(cycle_diagnoses),
                "normal_cycles": stage_counts.get("normal", 0),
                "early_warning_cycles": stage_counts.get("early_warning", 0),
                "confirmed_plating_cycles": stage_counts.get("confirmed_plating", 0),
                "severe_plating_cycles": stage_counts.get("severe_plating", 0),
                "average_risk_score": np.mean(risk_scores),
                "max_risk_score": np.max(risk_scores),
                "first_warning_cycle": min(warning_cycles) if warning_cycles else None,
            },
            "baseline_info": self.baseline_stats,
            "prediction": prediction,
            "recommendations": self._generate_recommendations(
                stage_counts, risk_scores, prediction
            ),
        }

        return battery_diagnosis

    def _determine_overall_status(
        self, stage_counts: Dict[str, int], risk_scores: List[float]
    ) -> str:
        """确定整体状态"""
        if stage_counts.get("severe_plating", 0) > 0:
            return "severe_degradation"
        elif stage_counts.get("confirmed_plating", 0) > 2:
            return "active_plating"
        elif stage_counts.get("early_warning", 0) > 3:
            return "early_warning"
        elif np.mean(risk_scores) > 0.3:
            return "moderate_risk"
        else:
            return "healthy"

    def _predict_future_degradation(
        self, data: pd.DataFrame, diagnoses: List[Dict]
    ) -> Dict[str, Any]:
        """预测未来衰减趋势"""
        # 简化预测逻辑
        recent_cycles = 10
        recent_data = data.tail(recent_cycles)
        recent_diagnoses = (
            diagnoses[-recent_cycles:] if len(diagnoses) >= recent_cycles else diagnoses
        )

        # 计算趋势
        risk_trend = np.mean([d["risk_score"] for d in recent_diagnoses])
        warning_frequency = sum(
            1 for d in recent_diagnoses if d["stage"] != "normal"
        ) / len(recent_diagnoses)

        prediction = {
            "risk_trend": (
                "increasing"
                if risk_trend > 0.4
                else "stable" if risk_trend > 0.2 else "low"
            ),
            "estimated_cycles_to_warning": None,
            "estimated_cycles_to_failure": None,
            "confidence": 0.6,
        }

        # 简单的线性外推预测
        if warning_frequency > 0.3:
            prediction["estimated_cycles_to_warning"] = max(
                5, int(50 * (1 - warning_frequency))
            )
            prediction["estimated_cycles_to_failure"] = max(
                20, int(200 * (1 - risk_trend))
            )

        return prediction

    def _generate_recommendations(
        self,
        stage_counts: Dict[str, int],
        risk_scores: List[float],
        prediction: Dict[str, Any],
    ) -> List[str]:
        """生成维护建议"""
        recommendations = []

        avg_risk = np.mean(risk_scores)

        if stage_counts.get("severe_plating", 0) > 0:
            recommendations.extend(
                [
                    "立即停止使用，电池已发生严重析锂",
                    "检查充电协议，降低充电电流",
                    "考虑更换电池",
                ]
            )
        elif stage_counts.get("confirmed_plating", 0) > 2:
            recommendations.extend(
                [
                    "确认析锂发生，建议调整充电策略",
                    "降低充电电流至0.5C以下",
                    "增加温度监控",
                    "缩短充电截止电压",
                ]
            )
        elif stage_counts.get("early_warning", 0) > 3:
            recommendations.extend(
                [
                    "检测到析锂早期信号，建议预防性维护",
                    "优化充电温度（保持在15-25°C）",
                    "避免低温充电",
                    "考虑降低充电倍率",
                ]
            )
        elif avg_risk > 0.2:
            recommendations.extend(
                ["定期监控电池状态", "避免极端工况使用", "保持适宜的工作温度"]
            )
        else:
            recommendations.append("电池状态良好，继续监控")

        return recommendations


def save_diagnosis_report(diagnosis_result: Dict[str, Any], output_path: str):
    """保存诊断报告到JSON文件"""

    # 处理numpy类型，确保可以JSON序列化
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif pd.isna(obj):
            return None
        return obj

    def clean_dict(d):
        if isinstance(d, dict):
            return {k: clean_dict(v) for k, v in d.items()}
        elif isinstance(d, list):
            return [clean_dict(item) for item in d]
        else:
            return convert_numpy(d)

    cleaned_result = clean_dict(diagnosis_result)

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(cleaned_result, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    # 示例用法
    print("析锂诊断模型模块")
    print("用法: 从 soft_metrics_analyzer.py 中调用，或者：")
    print("from lithium_plating_diagnostic import LithiumPlatingDiagnostic")
    print("diagnostic = LithiumPlatingDiagnostic()")
    print("result = diagnostic.diagnose_battery(data)")
