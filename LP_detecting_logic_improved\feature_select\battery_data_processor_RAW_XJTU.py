"""
电池数据提取与处理模块
Battery Data Extraction and Processing Module

包含所有数据提取、处理和分析功能，不包含绘图代码
Contains all data extraction, processing and analysis functions without plotting code
"""

import os
import numpy as np
import pandas as pd
import json
import time
import logging
from scipy.signal import savgol_filter
from scipy.interpolate import interp1d

# Set up logging
logging.basicConfig(level=logging.INFO, format="[%(asctime)s] %(message)s")


def log(message):
    """Record log messages"""
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}")
    logging.info(message)


def compute_dIdt_vectorized(time_s, current_A, internal=50):
    """Calculate dI/dt using vectorized method for better performance"""
    if len(time_s) < 2:
        return None, None

    try:
        # Convert to numpy arrays for vector operations
        time_array = np.array(time_s)
        current_array = np.array(current_A)

        # Calculate time differences and current differences
        dt = np.diff(time_array)
        dI = np.diff(current_array)

        # 大幅降低阈值，使更多数据点被保留
        # Calculate threshold - 使用更低的internal值来降低阈值
        max_current = np.max(np.abs(current_array))
        threshold = max_current / (internal * 10)  # 将阈值降低10倍

        # 只过滤掉时间差无效的点
        valid_mask = np.abs(dt) > 1e-10

        # 计算所有有效点的dI/dt
        dIdt_values = dI[valid_mask] / dt[valid_mask]
        didt_times = time_array[:-1][valid_mask]

        if len(dIdt_values) == 0:
            return None, None

        # Fix: Ensure we're not returning NaN or infinite values
        valid_values = ~np.isnan(dIdt_values) & ~np.isinf(dIdt_values)
        if not np.all(valid_values):
            dIdt_values = dIdt_values[valid_values]
            didt_times = didt_times[valid_values]

        if len(dIdt_values) == 0:
            return None, None

        # 对dI/dt值进行平滑处理，减少噪声
        if len(dIdt_values) > 5:
            window_size = min(5, len(dIdt_values) - (len(dIdt_values) % 2 == 0))
            if window_size >= 3:
                try:
                    dIdt_values = savgol_filter(dIdt_values, window_size, 2)
                except Exception:
                    pass  # 如果平滑失败，使用原始值

        # 确保返回的是numpy数组而不是视图
        return np.array(dIdt_values), np.array(didt_times)
    except Exception as e:
        log(f"Error in compute_dIdt_vectorized: {str(e)}")
        return None, None


def process_cycle(cycle_data):
    """Process a single cycle to calculate dI/dt"""
    cycle_num, data, downsample_params = cycle_data

    # Apply adaptive downsampling based on current threshold
    if isinstance(downsample_params, dict):
        # Adaptive downsampling based on current magnitude
        i_threshold = downsample_params.get("i_threshold", 0.5)
        factor_high = downsample_params.get(
            "factor_high", 1
        )  # For |current| > threshold
        factor_low = downsample_params.get(
            "factor_low", 1
        )  # For |current| <= threshold

        # Split data based on current threshold
        current_abs = np.abs(data["Current(A)"])
        high_current_mask = current_abs > i_threshold
        low_current_mask = current_abs <= i_threshold

        # Apply different downsampling factors
        processed_data_parts = []

        if high_current_mask.any() and factor_high > 1:
            high_current_data = data[high_current_mask]
            high_indices = np.arange(0, len(high_current_data), factor_high)
            if len(high_indices) > 0:
                high_current_data = high_current_data.iloc[high_indices].copy()
            processed_data_parts.append(high_current_data)
        elif high_current_mask.any():
            processed_data_parts.append(data[high_current_mask])

        if low_current_mask.any() and factor_low > 1:
            low_current_data = data[low_current_mask]
            low_indices = np.arange(0, len(low_current_data), factor_low)
            if len(low_indices) > 0:
                low_current_data = low_current_data.iloc[low_indices].copy()
            processed_data_parts.append(low_current_data)
        elif low_current_mask.any():
            processed_data_parts.append(data[low_current_mask])

        # Combine and sort by time
        if processed_data_parts:
            data = pd.concat(processed_data_parts).sort_values(by="Time(s)")

    else:
        # Traditional uniform downsampling
        downsample_factor = downsample_params
        if downsample_factor > 1:
            indices = np.arange(0, len(data), downsample_factor)
            if len(indices) > 0:
                data = data.iloc[indices].copy()

    # Extract time and current data
    time_s = data["Time(s)"]  # 这里已经是秒为单位的时间
    current_A = data["Current(A)"]

    # 简化调试信息，只在第一个循环或特定循环输出
    if cycle_num <= 3 or cycle_num % 50 == 0:
        print(
            f"Processing cycle {cycle_num}: {len(data)} data points, time range: {time_s.min():.1f}s - {time_s.max():.1f}s"
        )

    # Calculate dI/dt
    didt_values, didt_times = compute_dIdt_vectorized(time_s, current_A)

    if didt_values is not None and didt_times is not None:
        # 只在第一个循环或特定循环输出调试信息
        if cycle_num <= 3 or cycle_num % 50 == 0:
            print(
                f"  Cycle {cycle_num}: {len(didt_values)} dI/dt points, range: {min(didt_values):.3f} to {max(didt_values):.3f} A/s"
            )

        # 确保时间单位是秒
        # Create dI/dt dataframe
        df_dict = {"Time(s)": didt_times, "dI/dt_raw(A/s)": didt_values}

        # 如果原始数据中有分钟时间，也保存下来
        if "Time(min)" in data.columns:
            # 通过插值找到对应的分钟时间
            # 创建一个从秒到分钟的映射函数
            if len(data["Time(s)"]) > 1 and len(data["Time(min)"]) > 1:
                try:
                    sec_to_min = interp1d(
                        data["Time(s)"].values,
                        data["Time(min)"].values,
                        bounds_error=False,
                        fill_value="extrapolate",
                    )
                    # 计算对应的分钟时间
                    min_times = sec_to_min(didt_times)
                    df_dict["Time(min)"] = min_times
                except Exception as e:
                    if cycle_num <= 3:
                        print(
                            f"  Warning: Could not interpolate minute times for cycle {cycle_num}"
                        )

        # Convert to pandas dataframe
        df_didt = pd.DataFrame(df_dict)

        # Add smoothed dI/dt using rolling window
        df_didt["dI/dt_smooth(A/s)"] = (
            df_didt["dI/dt_raw(A/s)"].rolling(window=5, center=True).mean()
        )

        # Fill NaN values
        df_didt["dI/dt_smooth(A/s)"] = df_didt["dI/dt_smooth(A/s)"].fillna(
            df_didt["dI/dt_raw(A/s)"]
        )

        # 确保数据按时间排序
        df_didt = df_didt.sort_values(by="Time(s)")

        return cycle_num, df_didt
    else:
        if cycle_num <= 10 or cycle_num % 20 == 0:
            print(f"  Warning: No valid dI/dt data for cycle {cycle_num}")
        return cycle_num, None


def process_dIdt_data(all_cycles_data, downsample_params=1):
    """Process all cycle data, calculate dI/dt with optional adaptive downsampling

    Parameters:
    - all_cycles_data: Dictionary of cycle data
    - downsample_params: Can be:
        - int: Traditional uniform downsampling factor
        - dict: Adaptive downsampling parameters with keys:
            - 'i_threshold': Current threshold (A)
            - 'factor_high': Downsampling factor for |current| > threshold
            - 'factor_low': Downsampling factor for |current| <= threshold
    """
    log("Calculating dI/dt for all cycles...")

    # Log downsampling configuration
    if isinstance(downsample_params, dict):
        log(f"Using adaptive downsampling:")
        log(f"  - Current threshold: {downsample_params.get('i_threshold', 0.5)} A")
        log(f"  - High current factor: {downsample_params.get('factor_high', 1)}")
        log(f"  - Low current factor: {downsample_params.get('factor_low', 1)}")
    else:
        log(f"Using uniform downsampling factor: {downsample_params}")

    didt_cycles = {}

    # Create a progress tracking function
    total_cycles = len(all_cycles_data)
    processed_cycles = 0

    for cycle, data in all_cycles_data.items():
        # Process single cycle
        cycle_num, df_didt = process_cycle((cycle, data, downsample_params))

        if df_didt is not None:
            didt_cycles[cycle_num] = df_didt
            log(
                f"  Processed cycle {cycle_num}: calculated {len(df_didt)} dI/dt data points"
            )
        else:
            log(f"  Warning: Unable to calculate dI/dt for cycle {cycle_num}")

        # Update progress
        processed_cycles += 1
        if processed_cycles % 10 == 0 or processed_cycles == total_cycles:
            progress = (processed_cycles / total_cycles) * 100
            log(
                f"  Progress: {progress:.1f}% ({processed_cycles}/{total_cycles} cycles)"
            )

    log(f"dI/dt calculation complete, processed {len(didt_cycles)} cycles")
    return didt_cycles


def detect_knee_points(cycle_capacities, min_pts=3):
    """Automatically detect knee points in capacity data using curvature analysis"""
    if not cycle_capacities or len(cycle_capacities) < min_pts:
        return []

    try:
        # Sort cycles
        cycles = sorted(cycle_capacities.keys())
        capacities = [cycle_capacities[c] for c in cycles]

        if len(cycles) < 5:
            return []  # Not enough data for reliable detection

        # Convert to numpy arrays for numerical operations
        x = np.array(cycles)
        y = np.array(capacities)

        # Apply Savitzky-Golay filter to smooth the data
        if len(x) >= 5:
            window_size = min(
                len(x) - (len(x) % 2 == 0), 5
            )  # Make sure window size is odd and <= len(x)
            if window_size >= 3:
                y_smooth = savgol_filter(
                    y, window_size, 2
                )  # Window size 5, polynomial order 2
            else:
                y_smooth = y
        else:
            y_smooth = y

        # Calculate first and second derivatives
        dy = np.gradient(y_smooth)
        d2y = np.gradient(dy)

        # Calculate curvature: κ = |y''| / (1 + y'^2)^(3/2)
        curvature = np.abs(d2y) / (1 + dy**2) ** 1.5

        # Find local maxima in curvature above threshold (knee points)
        knee_indices = []
        threshold = 0.1 * np.max(curvature)

        # Skip first and last few points to avoid edge effects
        for i in range(2, len(curvature) - 2):
            if (
                curvature[i] > threshold
                and curvature[i] > curvature[i - 1]
                and curvature[i] > curvature[i + 1]
            ):
                knee_indices.append(i)

        # Limit to top 3 knee points
        if knee_indices:
            # Sort by curvature value (descending)
            knee_indices = sorted(
                knee_indices, key=lambda i: curvature[i], reverse=True
            )
            knee_indices = knee_indices[:3]  # Take top 3

        knee_cycles = [cycles[i] for i in knee_indices]
        return knee_cycles

    except Exception as e:
        log(f"Error in knee point detection: {str(e)}")
        return []


def extract_cycles_from_json(json_file_path):
    """Extract cycle data from JSON file with improved error handling"""
    log(f"Loading JSON file: {json_file_path}")

    try:
        with open(json_file_path, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except Exception as e:
        log(f"Error loading JSON file: {str(e)}")
        return None, None, None

    log("Extracting cycle data from JSON...")

    all_cycles_data = {}
    cycle_capacities = {}
    cycle_ce_values = {}  # New dictionary to store CE values

    # Verify data structure
    if not isinstance(json_data, dict):
        log("Error: Invalid JSON data format - expected a dictionary")
        return None, None, None

    # Extract cycle data from JSON
    for key in json_data.keys():
        if key.startswith("Cycle_"):
            try:
                cycle_num = int(key.split("_")[1])
                cycle_data = json_data[key]

                # Verify required fields exist
                if not isinstance(cycle_data, dict):
                    log(f"  Warning: Invalid data format for cycle {key}")
                    continue

                # Get time data and current data
                time_min = cycle_data.get("relative_time_min", [])
                current_A = cycle_data.get("current_A", [])

                if not time_min or not current_A:
                    log(f"  Warning: Missing time or current data for cycle {key}")
                    continue

                # Check arrays have same length
                if len(time_min) != len(current_A):
                    log(
                        f"  Warning: Time and current arrays have different lengths in cycle {key}"
                    )
                    # Use the shorter length
                    min_len = min(len(time_min), len(current_A))
                    time_min = time_min[:min_len]
                    current_A = current_A[:min_len]

                if len(time_min) > 0 and len(current_A) > 0:
                    # 确保正确转换时间单位：从分钟转换为秒
                    # 首先归一化时间（从0开始）
                    start_time = time_min[0]
                    # 然后将分钟转换为秒
                    time_s = [(t - start_time) * 60 for t in time_min]

                    # Create dataframe
                    df = pd.DataFrame({"Time(s)": time_s, "Current(A)": current_A})
                    # 保存原始分钟数据，以便后续处理
                    df["Time(min)"] = [t - start_time for t in time_min]

                    all_cycles_data[cycle_num] = df
                    log(
                        f"  Extracted cycle {cycle_num}, containing {len(df)} data points"
                    )
            except (ValueError, IndexError, TypeError) as e:
                log(f"  Error processing cycle {key}: {str(e)}")
                continue

    # Extract discharge capacity data (if exists)
    if "Discharge Capacity" in json_data:
        try:
            discharge_capacities = json_data["Discharge Capacity"]
            if isinstance(discharge_capacities, list):
                # Match capacities to cycles
                for i, capacity in enumerate(discharge_capacities):
                    cycle_num = i + 1  # Assuming capacity data starts from cycle 1
                    if (
                        cycle_num in all_cycles_data
                    ):  # Only store capacity for cycles with data
                        # Check if capacity is numeric
                        try:
                            capacity_value = float(capacity)
                            cycle_capacities[cycle_num] = capacity_value
                            log(
                                f"  Extracted capacity for cycle {cycle_num}: {capacity_value:.4f} Ah"
                            )
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric capacity value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'Discharge Capacity' is not a list")
        except Exception as e:
            log(f"  Error processing capacity data: {str(e)}")
    else:
        log(f"  Note: 'Discharge Capacity' data not found in JSON")

    # Extract CE data (if exists)
    if "CE" in json_data:
        try:
            ce_values = json_data["CE"]
            if isinstance(ce_values, list):
                # Match CE values to cycles
                for i, ce in enumerate(ce_values):
                    cycle_num = i + 1  # Assuming CE data starts from cycle 1
                    if (
                        cycle_num in all_cycles_data
                    ):  # Only store CE for cycles with data
                        # Check if CE is numeric
                        try:
                            ce_value = float(ce)
                            cycle_ce_values[cycle_num] = ce_value
                            log(f"  Extracted CE for cycle {cycle_num}: {ce_value:.6f}")
                        except (ValueError, TypeError):
                            log(
                                f"  Warning: Non-numeric CE value for cycle {cycle_num}"
                            )
            else:
                log(f"  Warning: 'CE' is not a list")
        except Exception as e:
            log(f"  Error processing CE data: {str(e)}")
    else:
        log(f"  Note: 'CE' data not found in JSON")

    log(f"Data extraction complete, {len(all_cycles_data)} cycles found")
    return all_cycles_data, cycle_capacities, cycle_ce_values


# ============================================================================
# 辅助函数和数据分析功能
# Helper Functions and Data Analysis Features
# ============================================================================


def extract_sample_name_from_path(file_path):
    """从文件路径提取样品名称"""
    from pathlib import Path

    filename = Path(file_path).stem

    # 移除常见后缀
    suffixes_to_remove = ["_CV_Qcha_CE", "_data", "_battery", "_cycle", "_test"]
    sample_name = filename

    for suffix in suffixes_to_remove:
        if sample_name.endswith(suffix):
            sample_name = sample_name[: -len(suffix)]

    return sample_name


def calculate_capacity_fade_rate(cycle_capacities):
    """计算容量衰减率"""
    if not cycle_capacities or len(cycle_capacities) < 2:
        return None

    cycles = sorted(cycle_capacities.keys())
    capacities = [cycle_capacities[c] for c in cycles]

    # 计算线性衰减率 (Ah/cycle)
    if len(cycles) > 2:
        # 使用线性拟合
        coeffs = np.polyfit(cycles, capacities, 1)
        fade_rate = coeffs[0]  # 斜率即为衰减率
    else:
        # 简单计算
        fade_rate = (capacities[-1] - capacities[0]) / (cycles[-1] - cycles[0])

    return fade_rate


def calculate_capacity_retention(cycle_capacities):
    """计算容量保持率"""
    if not cycle_capacities:
        return None

    cycles = sorted(cycle_capacities.keys())
    if len(cycles) < 2:
        return None

    initial_capacity = cycle_capacities[cycles[0]]
    final_capacity = cycle_capacities[cycles[-1]]

    if initial_capacity > 0:
        retention = (final_capacity / initial_capacity) * 100
        return retention

    return None


def analyze_didt_statistics(didt_cycles_data):
    """分析dI/dt数据的统计信息"""
    if not didt_cycles_data:
        return {}

    stats = {}
    all_didt_values = []

    for cycle_num, didt_data in didt_cycles_data.items():
        if didt_data is not None and "dI/dt_smooth(A/s)" in didt_data.columns:
            didt_values = didt_data["dI/dt_smooth(A/s)"].values
            valid_values = didt_values[~np.isnan(didt_values) & ~np.isinf(didt_values)]

            if len(valid_values) > 0:
                cycle_stats = {
                    "mean": np.mean(valid_values),
                    "std": np.std(valid_values),
                    "min": np.min(valid_values),
                    "max": np.max(valid_values),
                    "data_points": len(valid_values),
                }
                stats[cycle_num] = cycle_stats
                all_didt_values.extend(valid_values)

    # 全局统计
    if all_didt_values:
        stats["global"] = {
            "mean": np.mean(all_didt_values),
            "std": np.std(all_didt_values),
            "min": np.min(all_didt_values),
            "max": np.max(all_didt_values),
            "total_points": len(all_didt_values),
        }

    return stats


def get_cycle_summary(
    all_cycles_data, cycle_capacities, cycle_ce_values, didt_cycles_data
):
    """获取数据集的完整摘要信息"""
    summary = {
        "total_cycles": len(all_cycles_data),
        "cycles_with_capacity": len(cycle_capacities) if cycle_capacities else 0,
        "cycles_with_ce": len(cycle_ce_values) if cycle_ce_values else 0,
        "cycles_with_didt": len(didt_cycles_data) if didt_cycles_data else 0,
        "cycle_range": None,
        "capacity_info": {},
        "ce_info": {},
        "data_quality": {},
    }

    if all_cycles_data:
        cycles = sorted(all_cycles_data.keys())
        summary["cycle_range"] = (cycles[0], cycles[-1])

        # 数据点统计
        total_data_points = sum(len(data) for data in all_cycles_data.values())
        avg_points_per_cycle = total_data_points / len(all_cycles_data)

        summary["data_quality"] = {
            "total_data_points": total_data_points,
            "avg_points_per_cycle": avg_points_per_cycle,
            "min_points_per_cycle": min(len(data) for data in all_cycles_data.values()),
            "max_points_per_cycle": max(len(data) for data in all_cycles_data.values()),
        }

    # 容量信息
    if cycle_capacities:
        capacities = list(cycle_capacities.values())
        fade_rate = calculate_capacity_fade_rate(cycle_capacities)
        retention = calculate_capacity_retention(cycle_capacities)

        summary["capacity_info"] = {
            "initial_capacity": capacities[0] if capacities else None,
            "final_capacity": capacities[-1] if capacities else None,
            "capacity_range": (
                (min(capacities), max(capacities)) if capacities else None
            ),
            "fade_rate_ah_per_cycle": fade_rate,
            "retention_percentage": retention,
        }

    # CE信息
    if cycle_ce_values:
        ce_values = list(cycle_ce_values.values())
        summary["ce_info"] = {
            "mean_ce": np.mean(ce_values),
            "std_ce": np.std(ce_values),
            "min_ce": min(ce_values),
            "max_ce": max(ce_values),
        }

    return summary


def validate_data_consistency(all_cycles_data, cycle_capacities, cycle_ce_values):
    """验证数据一致性"""
    issues = []

    if not all_cycles_data:
        issues.append("No cycle data found")
        return issues

    cycle_nums = set(all_cycles_data.keys())

    # 检查容量数据一致性
    if cycle_capacities:
        capacity_cycles = set(cycle_capacities.keys())
        missing_capacity = cycle_nums - capacity_cycles
        extra_capacity = capacity_cycles - cycle_nums

        if missing_capacity:
            issues.append(
                f"Missing capacity data for cycles: {sorted(missing_capacity)}"
            )
        if extra_capacity:
            issues.append(
                f"Extra capacity data for non-existent cycles: {sorted(extra_capacity)}"
            )

    # 检查CE数据一致性
    if cycle_ce_values:
        ce_cycles = set(cycle_ce_values.keys())
        missing_ce = cycle_nums - ce_cycles
        extra_ce = ce_cycles - cycle_nums

        if missing_ce:
            issues.append(f"Missing CE data for cycles: {sorted(missing_ce)}")
        if extra_ce:
            issues.append(f"Extra CE data for non-existent cycles: {sorted(extra_ce)}")

    # 检查数据质量
    for cycle_num, data in all_cycles_data.items():
        if len(data) < 10:
            issues.append(f"Cycle {cycle_num} has very few data points ({len(data)})")

        # 检查时间序列
        time_values = data["Time(s)"].values
        if not np.all(np.diff(time_values) >= 0):
            issues.append(f"Cycle {cycle_num} has non-monotonic time values")

        # 检查电流值
        current_values = data["Current(A)"].values
        if np.any(np.isnan(current_values)) or np.any(np.isinf(current_values)):
            issues.append(f"Cycle {cycle_num} has invalid current values (NaN or Inf)")

    return issues


def export_processed_data(
    all_cycles_data, didt_cycles_data, cycle_capacities, cycle_ce_values, output_path
):
    """导出处理后的数据到文件"""
    try:
        import pickle

        data_package = {
            "all_cycles_data": all_cycles_data,
            "didt_cycles_data": didt_cycles_data,
            "cycle_capacities": cycle_capacities,
            "cycle_ce_values": cycle_ce_values,
            "processing_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": get_cycle_summary(
                all_cycles_data, cycle_capacities, cycle_ce_values, didt_cycles_data
            ),
        }

        with open(output_path, "wb") as f:
            pickle.dump(data_package, f)

        log(f"Processed data exported to: {output_path}")
        return True

    except Exception as e:
        log(f"Error exporting data: {str(e)}")
        return False


def load_processed_data(input_path):
    """从文件加载处理后的数据"""
    try:
        import pickle

        with open(input_path, "rb") as f:
            data_package = pickle.load(f)

        log(f"Processed data loaded from: {input_path}")
        log(f"Data processed on: {data_package.get('processing_timestamp', 'Unknown')}")

        return (
            data_package["all_cycles_data"],
            data_package["didt_cycles_data"],
            data_package["cycle_capacities"],
            data_package["cycle_ce_values"],
        )

    except Exception as e:
        log(f"Error loading data: {str(e)}")
        return None, None, None, None
